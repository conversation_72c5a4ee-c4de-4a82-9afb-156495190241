"""
Web Data Automation 主程序
自动化网页数据获取和推送系统
"""

import os
import sys
import logging
import traceback
from datetime import datetime
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.config.settings import Config
from src.core.web_scraper import WebScraper
from src.core.data_processor import DataProcessor
from src.core.file_manager import FileManager
from src.integrations.chat_sender import ChatSenderFactory
from src.scheduler.task_scheduler import TaskScheduler
from src.utils.logger import setup_logging


class WebDataAutomation:
    """Web数据自动化主类"""
    
    def __init__(self, config_path: str = None):
        """
        初始化自动化系统
        
        Args:
            config_path: 配置文件路径
        """
        # 加载配置
        self.config = Config(config_path)
        
        # 设置日志
        setup_logging(self.config)
        self.logger = logging.getLogger(__name__)
        
        # 初始化组件
        self.web_scraper = None
        self.data_processor = DataProcessor(self.config)
        self.file_manager = FileManager(self.config)
        self.chat_sender = ChatSenderFactory.create_sender(self.config)
        self.scheduler = TaskScheduler(self.config)
        
        self.logger.info("Web Data Automation initialized successfully")
    
    def run_single_task(self) -> bool:
        """
        执行单次数据获取任务
        
        Returns:
            bool: 任务是否成功完成
        """
        try:
            self.logger.info("Starting single data extraction task")
            
            # 1. 初始化Web爬虫
            with WebScraper(self.config) as scraper:
                self.web_scraper = scraper
                
                # 2. 登录网站
                if not scraper.login():
                    raise Exception("Failed to login to target website")
                
                # 3. 导航到目标页面
                if not scraper.navigate_to_target_page():
                    raise Exception("Failed to navigate to target page")
                
                # 4. 应用筛选条件
                if not scraper.apply_filters():
                    raise Exception("Failed to apply filters")
                
                # 5. 获取cookies并设置requests会话
                scraper.get_cookies()
                
                # 6. 获取页面数据
                html_content = scraper.fetch_data()
                if not html_content:
                    raise Exception("Failed to fetch data from target page")
            
            # 7. 处理数据
            df, validation_result = self.data_processor.process_data(html_content)
            
            if not validation_result['is_valid']:
                error_msg = f"Data validation failed: {validation_result['errors']}"
                self.logger.error(error_msg)
                if self.chat_sender:
                    self.chat_sender.send_message(f"❌ 数据验证失败: {validation_result['errors']}")
                return False
            
            # 8. 保存文件
            file_paths = self.file_manager.save_data(df)
            if not file_paths:
                raise Exception("Failed to save data files")
            
            # 9. 发送到聊天软件
            success_count = 0
            for file_path in file_paths:
                if self.chat_sender and self.chat_sender.send_file(file_path):
                    success_count += 1
            
            # 10. 记录任务完成
            stats = validation_result.get('statistics', {})
            completion_message = (
                f"✅ 数据提取任务完成\n"
                f"📊 数据行数: {stats.get('total_rows', 0)}\n"
                f"📁 生成文件: {len(file_paths)}\n"
                f"📤 发送成功: {success_count}/{len(file_paths)}\n"
                f"⏰ 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            )
            
            self.logger.info(completion_message.replace('\n', ' | '))
            
            if self.chat_sender:
                self.chat_sender.send_message(completion_message)
            
            return True
            
        except Exception as e:
            error_msg = f"Task execution failed: {str(e)}"
            self.logger.error(error_msg)
            self.logger.error(traceback.format_exc())
            
            if self.chat_sender:
                self.chat_sender.send_message(f"❌ 任务执行失败: {str(e)}")
            
            return False
    
    def start_scheduler(self):
        """启动定时任务调度器"""
        try:
            self.logger.info("Starting task scheduler")
            
            # 注册任务
            self.scheduler.register_job(
                name="data_extraction",
                func=self.run_single_task,
                schedule=self.config.get('scheduler.jobs.0.schedule', '0 9 * * *')
            )
            
            # 启动调度器
            self.scheduler.start()
            
        except Exception as e:
            self.logger.error(f"Failed to start scheduler: {str(e)}")
            raise
    
    def run_interactive_mode(self):
        """运行交互模式"""
        print("\n" + "="*50)
        print("Web Data Automation - 交互模式")
        print("="*50)
        
        while True:
            print("\n请选择操作:")
            print("1. 执行单次数据提取")
            print("2. 启动定时任务")
            print("3. 测试聊天软件连接")
            print("4. 查看配置信息")
            print("5. 退出程序")
            
            choice = input("\n请输入选择 (1-5): ").strip()
            
            if choice == '1':
                print("\n开始执行数据提取任务...")
                success = self.run_single_task()
                if success:
                    print("✅ 任务执行成功!")
                else:
                    print("❌ 任务执行失败!")
            
            elif choice == '2':
                print("\n启动定时任务调度器...")
                try:
                    self.start_scheduler()
                    print("✅ 定时任务已启动，按 Ctrl+C 停止")
                except KeyboardInterrupt:
                    print("\n⏹️ 定时任务已停止")
                except Exception as e:
                    print(f"❌ 启动失败: {str(e)}")
            
            elif choice == '3':
                print("\n测试聊天软件连接...")
                if self.chat_sender:
                    success = self.chat_sender.send_message("🔧 聊天软件连接测试")
                    if success:
                        print("✅ 聊天软件连接正常!")
                    else:
                        print("❌ 聊天软件连接失败!")
                else:
                    print("❌ 未配置聊天软件")
            
            elif choice == '4':
                print("\n配置信息:")
                print(f"目标URL: {self.config.get('web.target_url', '未配置')}")
                print(f"输出路径: {self.config.get('output.base_path', '未配置')}")
                print(f"聊天软件: {'已配置' if self.chat_sender else '未配置'}")
                print(f"定时任务: {'启用' if self.config.get('scheduler.enabled') else '禁用'}")
            
            elif choice == '5':
                print("\n👋 程序退出")
                break
            
            else:
                print("❌ 无效选择，请重新输入")


def main():
    """主函数"""
    try:
        # 检查配置文件
        config_path = os.path.join(os.path.dirname(__file__), 'config', 'config.yaml')
        if not os.path.exists(config_path):
            print(f"❌ 配置文件不存在: {config_path}")
            print("请先创建配置文件")
            return
        
        # 初始化自动化系统
        automation = WebDataAutomation(config_path)
        
        # 检查命令行参数
        if len(sys.argv) > 1:
            command = sys.argv[1].lower()
            
            if command == 'run':
                # 执行单次任务
                success = automation.run_single_task()
                sys.exit(0 if success else 1)
            
            elif command == 'schedule':
                # 启动定时任务
                automation.start_scheduler()
            
            elif command == 'test':
                # 测试模式
                print("🔧 测试模式")
                if automation.chat_sender:
                    automation.chat_sender.send_message("🔧 系统测试消息")
                print("✅ 测试完成")
            
            else:
                print(f"❌ 未知命令: {command}")
                print("可用命令: run, schedule, test")
                sys.exit(1)
        
        else:
            # 交互模式
            automation.run_interactive_mode()
    
    except KeyboardInterrupt:
        print("\n\n👋 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序执行失败: {str(e)}")
        logging.error(traceback.format_exc())
        sys.exit(1)


if __name__ == "__main__":
    main()
