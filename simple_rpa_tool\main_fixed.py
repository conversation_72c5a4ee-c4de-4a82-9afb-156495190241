"""
Simple RPA Tool - 修复版主程序
适用于小白的RPA工具 - 兼容性优化版本
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, messagebox
import json
from pathlib import Path

# 获取应用程序根目录
def get_app_root():
    if getattr(sys, 'frozen', False):
        # 如果是打包后的exe
        return Path(sys._MEIPASS)
    else:
        # 如果是开发环境
        return Path(__file__).parent

# 设置项目根目录
project_root = get_app_root()
sys.path.insert(0, str(project_root))

# 安全导入核心模块
try:
    from gui.main_window import MainWindow
    from core.task_engine import TaskEngine
    GUI_AVAILABLE = True
except ImportError as e:
    print(f"GUI模块导入失败: {e}")
    print(f"当前Python路径: {sys.path}")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"项目根目录: {project_root}")
    GUI_AVAILABLE = False


class SimpleRPATool:
    """简单RPA工具主类 - 修复版"""
    
    def __init__(self):
        """初始化RPA工具"""
        if not GUI_AVAILABLE:
            raise ImportError("GUI模块不可用，无法启动程序")
        
        self.root = tk.Tk()
        self.root.title("Simple RPA Tool - 小白专用RPA工具 (修复版)")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)
        
        # 设置图标和样式
        self.setup_styles()
        
        # 初始化核心组件
        self.task_engine = TaskEngine()
        
        # 创建主窗口
        try:
            self.main_window = MainWindow(self.root, self.task_engine)
        except Exception as e:
            messagebox.showerror("初始化失败", f"主窗口创建失败: {str(e)}")
            raise
        
        # 设置关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def setup_styles(self):
        """设置界面样式"""
        try:
            # 设置主题
            style = ttk.Style()
            available_themes = style.theme_names()
            
            # 选择最佳主题
            if 'clam' in available_themes:
                style.theme_use('clam')
            elif 'alt' in available_themes:
                style.theme_use('alt')
            else:
                style.theme_use(available_themes[0])
            
            # 自定义样式
            style.configure('Title.TLabel', font=('Arial', 16, 'bold'))
            style.configure('Heading.TLabel', font=('Arial', 12, 'bold'))
            style.configure('Action.TButton', padding=(10, 5))
            
        except Exception as e:
            print(f"样式设置失败: {e}")
    
    def on_closing(self):
        """程序关闭时的处理"""
        try:
            # 停止所有正在运行的任务
            if hasattr(self, 'task_engine'):
                self.task_engine.stop_all_tasks()
            
            # 保存用户设置
            self.save_user_settings()
            
            # 关闭程序
            self.root.destroy()
            
        except Exception as e:
            print(f"关闭程序时出错: {e}")
            self.root.destroy()
    
    def save_user_settings(self):
        """保存用户设置"""
        try:
            settings = {
                'window_geometry': self.root.geometry(),
                'last_project_path': getattr(self.main_window, 'current_project_path', ''),
                'recent_projects': getattr(self.main_window, 'recent_projects', [])
            }
            
            config_dir = project_root / 'config'
            config_dir.mkdir(exist_ok=True)
            
            with open(config_dir / 'user_settings.json', 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"保存用户设置失败: {e}")
    
    def load_user_settings(self):
        """加载用户设置"""
        try:
            config_file = project_root / 'config' / 'user_settings.json'
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                
                # 恢复窗口大小和位置
                if 'window_geometry' in settings:
                    self.root.geometry(settings['window_geometry'])
                
                # 恢复最近项目
                if hasattr(self.main_window, 'load_recent_projects'):
                    self.main_window.load_recent_projects(settings.get('recent_projects', []))
                    
        except Exception as e:
            print(f"加载用户设置失败: {e}")
    
    def run(self):
        """运行RPA工具"""
        try:
            # 加载用户设置
            self.load_user_settings()
            
            # 显示欢迎信息
            self.show_welcome_message()
            
            # 启动主循环
            self.root.mainloop()
            
        except Exception as e:
            messagebox.showerror("运行失败", f"程序运行失败: {str(e)}")
    
    def show_welcome_message(self):
        """显示欢迎信息"""
        # 在状态栏显示欢迎信息
        if hasattr(self.main_window, 'status_bar'):
            self.main_window.status_bar.config(text="欢迎使用 Simple RPA Tool 修复版！基本功能已就绪。")


def check_environment():
    """检查运行环境"""
    issues = []
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        issues.append("Python版本过低，需要3.7或更高版本")
    
    # 检查tkinter
    try:
        import tkinter
        import tkinter.ttk
    except ImportError:
        issues.append("tkinter不可用，请检查Python安装")
    
    return issues


def main():
    """主函数"""
    print("Simple RPA Tool - 修复版启动中...")
    
    # 检查环境
    issues = check_environment()
    if issues:
        error_msg = "环境检查失败:\n" + "\n".join(f"- {issue}" for issue in issues)
        print(error_msg)
        try:
            messagebox.showerror("环境错误", error_msg)
        except:
            pass
        return
    
    try:
        # 创建必要的目录
        for dir_name in ['config', 'templates', 'output', 'logs']:
            (project_root / dir_name).mkdir(exist_ok=True)
        
        # 启动RPA工具
        app = SimpleRPATool()
        app.run()
        
    except ImportError as e:
        error_msg = f"模块导入失败: {str(e)}\n\n可能的解决方案:\n1. 检查文件结构是否完整\n2. 确保所有Python文件都存在\n3. 重新下载完整的项目文件"
        print(error_msg)
        try:
            messagebox.showerror("导入失败", error_msg)
        except:
            pass
    except Exception as e:
        error_msg = f"程序启动失败: {str(e)}"
        print(error_msg)
        try:
            messagebox.showerror("启动失败", error_msg)
        except:
            pass


if __name__ == "__main__":
    main()
