# Web Data Automation Project

## 项目概述

这是一个自动化数据获取和推送系统，具备以下核心功能：

1. **Web数据获取**: 使用Selenium登录网页，结合Requests获取数据
2. **数据处理**: 自动清洗、转换和格式化数据
3. **文件导出**: 支持Excel、CSV等格式导出
4. **自动推送**: 将数据文件推送到内网聊天软件群聊
5. **定时执行**: 支持灵活的定时任务配置
6. **易于维护**: 模块化设计，配置文件管理

## 项目结构

```
web_data_automation/
├── src/                      # 源代码目录
│   ├── config/              # 配置管理
│   ├── core/                # 核心功能模块
│   ├── integrations/        # 第三方集成
│   ├── scheduler/           # 定时任务
│   └── utils/               # 工具函数
├── config/                  # 配置文件
├── output/                  # 输出文件
├── logs/                    # 日志文件
├── tests/                   # 测试文件
└── main.py                  # 主程序入口
```

## 功能特性

### 1. Web数据获取
- Selenium自动化登录
- 智能等待和错误处理
- Cookie会话管理
- 多种元素定位策略

### 2. 数据处理
- 自动数据清洗
- 多格式数据转换
- 数据质量验证
- 自定义处理规则

### 3. 文件管理
- 自动文件命名
- 多格式导出支持
- 文件压缩和归档
- 存储路径管理

### 4. 聊天软件集成
- 企业微信支持
- 钉钉集成
- 飞书推送
- 自定义Webhook

### 5. 定时任务
- Cron表达式支持
- 任务状态监控
- 失败重试机制
- 任务历史记录

### 6. 系统监控
- 详细日志记录
- 性能监控
- 错误告警
- 运行状态报告

## 安装和使用

### 环境要求
- Python 3.8+
- Chrome浏览器
- 网络连接

### 安装步骤
1. 克隆项目
2. 安装依赖: `pip install -r requirements.txt`
3. 配置环境变量
4. 运行程序: `python main.py`

### 配置说明
详细配置请参考 `config/config.yaml` 文件

## 打包为可执行文件

使用PyInstaller将项目打包为单个exe文件：
```bash
pyinstaller --onefile --windowed main.py
```

## 维护和扩展

### 添加新的数据源
1. 在 `core/web_scraper.py` 中添加新的爬取逻辑
2. 更新配置文件
3. 测试新功能

### 添加新的聊天软件支持
1. 在 `integrations/chat_sender.py` 中添加新的发送器
2. 实现相应的API接口
3. 更新配置选项

### 自定义数据处理
1. 修改 `core/data_processor.py`
2. 添加新的处理函数
3. 配置处理规则

## 故障排除

常见问题和解决方案请参考项目Wiki。

## 许可证

MIT License
