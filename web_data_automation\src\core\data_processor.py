"""
数据处理模块
负责数据清洗、转换和验证
"""

import pandas as pd
import numpy as np
from datetime import datetime
import re
import logging
from typing import List, Dict, Any, Optional

logger = logging.getLogger(__name__)


class DataProcessor:
    """数据处理器"""
    
    def __init__(self, config):
        """
        初始化数据处理器
        
        Args:
            config: 配置对象
        """
        self.config = config
        self.cleaning_rules = config.get('data_processing.cleaning', {})
        self.transformation_rules = config.get('data_processing.transformation', {})
        self.validation_rules = config.get('data_processing.validation', {})
    
    def parse_html_to_dataframe(self, html_content: str) -> pd.DataFrame:
        """
        将HTML内容解析为DataFrame
        
        Args:
            html_content: HTML字符串
            
        Returns:
            pd.DataFrame: 解析后的数据框
        """
        try:
            # 使用pandas读取HTML表格
            dfs = pd.read_html(html_content)
            
            if not dfs:
                logger.warning("No tables found in HTML content")
                return pd.DataFrame()
            
            # 默认取第一个表格，可以根据需要修改
            df = dfs[0]
            logger.info(f"Successfully parsed HTML table with {len(df)} rows and {len(df.columns)} columns")
            
            return df
            
        except Exception as e:
            logger.error(f"Failed to parse HTML to DataFrame: {str(e)}")
            return pd.DataFrame()
    
    def clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        数据清洗
        
        Args:
            df: 原始数据框
            
        Returns:
            pd.DataFrame: 清洗后的数据框
        """
        try:
            cleaned_df = df.copy()
            
            # 删除空行
            if self.cleaning_rules.get('remove_empty_rows', True):
                cleaned_df = cleaned_df.dropna(how='all')
                logger.info(f"Removed empty rows, remaining: {len(cleaned_df)} rows")
            
            # 删除空列
            if self.cleaning_rules.get('remove_empty_columns', True):
                cleaned_df = cleaned_df.dropna(axis=1, how='all')
                logger.info(f"Removed empty columns, remaining: {len(cleaned_df.columns)} columns")
            
            # 去除字符串前后空格
            if self.cleaning_rules.get('strip_whitespace', True):
                for col in cleaned_df.select_dtypes(include=['object']).columns:
                    cleaned_df[col] = cleaned_df[col].astype(str).str.strip()
                logger.info("Stripped whitespace from string columns")
            
            # 处理重复行
            if self.cleaning_rules.get('remove_duplicates', False):
                before_count = len(cleaned_df)
                cleaned_df = cleaned_df.drop_duplicates()
                after_count = len(cleaned_df)
                logger.info(f"Removed {before_count - after_count} duplicate rows")
            
            # 处理异常值（可以根据需要扩展）
            cleaned_df = self._handle_outliers(cleaned_df)
            
            logger.info("Data cleaning completed successfully")
            return cleaned_df
            
        except Exception as e:
            logger.error(f"Data cleaning failed: {str(e)}")
            return df
    
    def transform_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        数据转换
        
        Args:
            df: 清洗后的数据框
            
        Returns:
            pd.DataFrame: 转换后的数据框
        """
        try:
            transformed_df = df.copy()
            
            # 日期列转换
            date_columns = self.transformation_rules.get('date_columns', [])
            for col in date_columns:
                if col in transformed_df.columns:
                    transformed_df[col] = pd.to_datetime(transformed_df[col], errors='coerce')
                    logger.info(f"Converted column '{col}' to datetime")
            
            # 数值列转换
            numeric_columns = self.transformation_rules.get('numeric_columns', [])
            for col in numeric_columns:
                if col in transformed_df.columns:
                    transformed_df[col] = pd.to_numeric(transformed_df[col], errors='coerce')
                    logger.info(f"Converted column '{col}' to numeric")
            
            # 列名标准化
            if self.transformation_rules.get('standardize_column_names', False):
                transformed_df.columns = [self._standardize_column_name(col) for col in transformed_df.columns]
                logger.info("Standardized column names")
            
            # 自定义转换规则
            transformed_df = self._apply_custom_transformations(transformed_df)
            
            logger.info("Data transformation completed successfully")
            return transformed_df
            
        except Exception as e:
            logger.error(f"Data transformation failed: {str(e)}")
            return df
    
    def validate_data(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        数据验证
        
        Args:
            df: 转换后的数据框
            
        Returns:
            Dict: 验证结果
        """
        validation_result = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'statistics': {}
        }
        
        try:
            # 检查必需列
            required_columns = self.validation_rules.get('required_columns', [])
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                validation_result['is_valid'] = False
                validation_result['errors'].append(f"Missing required columns: {missing_columns}")
            
            # 检查行数范围
            min_rows = self.validation_rules.get('min_rows', 1)
            max_rows = self.validation_rules.get('max_rows', 10000)
            
            if len(df) < min_rows:
                validation_result['is_valid'] = False
                validation_result['errors'].append(f"Too few rows: {len(df)} < {min_rows}")
            
            if len(df) > max_rows:
                validation_result['warnings'].append(f"Many rows: {len(df)} > {max_rows}")
            
            # 数据质量统计
            validation_result['statistics'] = {
                'total_rows': len(df),
                'total_columns': len(df.columns),
                'null_percentage': (df.isnull().sum().sum() / (len(df) * len(df.columns))) * 100,
                'duplicate_rows': df.duplicated().sum(),
                'memory_usage': df.memory_usage(deep=True).sum()
            }
            
            # 检查数据类型一致性
            type_issues = self._check_data_types(df)
            if type_issues:
                validation_result['warnings'].extend(type_issues)
            
            logger.info(f"Data validation completed. Valid: {validation_result['is_valid']}")
            return validation_result
            
        except Exception as e:
            logger.error(f"Data validation failed: {str(e)}")
            validation_result['is_valid'] = False
            validation_result['errors'].append(f"Validation error: {str(e)}")
            return validation_result
    
    def process_data(self, html_content: str) -> tuple:
        """
        完整的数据处理流程
        
        Args:
            html_content: HTML字符串
            
        Returns:
            tuple: (处理后的DataFrame, 验证结果)
        """
        try:
            # 1. 解析HTML
            df = self.parse_html_to_dataframe(html_content)
            if df.empty:
                return df, {'is_valid': False, 'errors': ['No data found']}
            
            # 2. 数据清洗
            df = self.clean_data(df)
            
            # 3. 数据转换
            df = self.transform_data(df)
            
            # 4. 数据验证
            validation_result = self.validate_data(df)
            
            logger.info("Complete data processing pipeline executed successfully")
            return df, validation_result
            
        except Exception as e:
            logger.error(f"Data processing pipeline failed: {str(e)}")
            return pd.DataFrame(), {'is_valid': False, 'errors': [str(e)]}
    
    def _handle_outliers(self, df: pd.DataFrame) -> pd.DataFrame:
        """处理异常值"""
        try:
            # 对数值列进行异常值检测和处理
            numeric_columns = df.select_dtypes(include=[np.number]).columns
            
            for col in numeric_columns:
                if self.cleaning_rules.get('handle_outliers', False):
                    Q1 = df[col].quantile(0.25)
                    Q3 = df[col].quantile(0.75)
                    IQR = Q3 - Q1
                    lower_bound = Q1 - 1.5 * IQR
                    upper_bound = Q3 + 1.5 * IQR
                    
                    # 可以选择删除或替换异常值
                    outlier_method = self.cleaning_rules.get('outlier_method', 'cap')
                    if outlier_method == 'remove':
                        df = df[(df[col] >= lower_bound) & (df[col] <= upper_bound)]
                    elif outlier_method == 'cap':
                        df[col] = df[col].clip(lower=lower_bound, upper=upper_bound)
            
            return df
            
        except Exception as e:
            logger.warning(f"Outlier handling failed: {str(e)}")
            return df
    
    def _standardize_column_name(self, column_name: str) -> str:
        """标准化列名"""
        # 转换为小写，替换空格和特殊字符为下划线
        standardized = re.sub(r'[^\w\s]', '', str(column_name))
        standardized = re.sub(r'\s+', '_', standardized)
        return standardized.lower().strip('_')
    
    def _apply_custom_transformations(self, df: pd.DataFrame) -> pd.DataFrame:
        """应用自定义转换规则"""
        try:
            # 这里可以添加特定的业务逻辑转换
            # 例如：计算新列、数据格式化等
            
            # 示例：添加处理时间戳
            df['processed_at'] = datetime.now()
            
            # 示例：数据分类
            # if 'amount' in df.columns:
            #     df['amount_category'] = pd.cut(df['amount'], bins=[0, 100, 1000, float('inf')], 
            #                                   labels=['Small', 'Medium', 'Large'])
            
            return df
            
        except Exception as e:
            logger.warning(f"Custom transformations failed: {str(e)}")
            return df
    
    def _check_data_types(self, df: pd.DataFrame) -> List[str]:
        """检查数据类型一致性"""
        issues = []
        
        try:
            for col in df.columns:
                # 检查混合数据类型
                if df[col].dtype == 'object':
                    unique_types = set(type(x).__name__ for x in df[col].dropna())
                    if len(unique_types) > 1:
                        issues.append(f"Column '{col}' has mixed data types: {unique_types}")
                
                # 检查数值列中的非数值
                if df[col].dtype in ['int64', 'float64']:
                    non_numeric = df[col].apply(lambda x: not isinstance(x, (int, float, type(None))))
                    if non_numeric.any():
                        issues.append(f"Column '{col}' contains non-numeric values")
            
            return issues
            
        except Exception as e:
            logger.warning(f"Data type checking failed: {str(e)}")
            return []
