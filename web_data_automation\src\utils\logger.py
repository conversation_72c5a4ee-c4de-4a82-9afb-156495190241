"""
日志工具模块
配置和管理系统日志
"""

import os
import logging
import logging.handlers
from datetime import datetime
from pathlib import Path


def setup_logging(config):
    """
    设置日志配置
    
    Args:
        config: 配置对象
    """
    try:
        # 获取日志配置
        log_config = config.get('logging', {})
        
        # 创建日志目录
        log_path = log_config.get('file.path', 'logs')
        os.makedirs(log_path, exist_ok=True)
        
        # 设置根日志级别
        log_level = getattr(logging, log_config.get('level', 'INFO').upper())
        
        # 创建根logger
        root_logger = logging.getLogger()
        root_logger.setLevel(log_level)
        
        # 清除现有的处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # 日志格式
        log_format = log_config.get('format', 
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        formatter = logging.Formatter(log_format)
        
        # 文件日志处理器
        if log_config.get('file.enabled', True):
            log_filename = log_config.get('file.filename', 'automation.log')
            log_filepath = os.path.join(log_path, log_filename)
            
            # 使用RotatingFileHandler进行日志轮转
            max_size = _parse_size(log_config.get('file.max_size', '10MB'))
            backup_count = log_config.get('file.backup_count', 5)
            
            file_handler = logging.handlers.RotatingFileHandler(
                log_filepath,
                maxBytes=max_size,
                backupCount=backup_count,
                encoding='utf-8'
            )
            file_handler.setLevel(log_level)
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)
        
        # 控制台日志处理器
        if log_config.get('console.enabled', True):
            console_level = getattr(logging, 
                log_config.get('console.level', 'INFO').upper())
            
            console_handler = logging.StreamHandler()
            console_handler.setLevel(console_level)
            console_handler.setFormatter(formatter)
            root_logger.addHandler(console_handler)
        
        # 记录日志配置完成
        logging.info("Logging configuration completed")
        
    except Exception as e:
        # 如果日志配置失败，使用基本配置
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        logging.error(f"Failed to setup logging configuration: {str(e)}")


def _parse_size(size_str: str) -> int:
    """
    解析大小字符串为字节数
    
    Args:
        size_str: 大小字符串，如 "10MB", "1GB"
        
    Returns:
        int: 字节数
    """
    size_str = size_str.upper().strip()
    
    if size_str.endswith('KB'):
        return int(size_str[:-2]) * 1024
    elif size_str.endswith('MB'):
        return int(size_str[:-2]) * 1024 * 1024
    elif size_str.endswith('GB'):
        return int(size_str[:-2]) * 1024 * 1024 * 1024
    else:
        # 默认为字节
        return int(size_str)


class TaskLogger:
    """任务专用日志器"""
    
    def __init__(self, task_name: str, log_path: str = "logs"):
        """
        初始化任务日志器
        
        Args:
            task_name: 任务名称
            log_path: 日志路径
        """
        self.task_name = task_name
        self.log_path = log_path
        self.logger = logging.getLogger(f"task.{task_name}")
        
        # 创建任务专用日志文件
        self._setup_task_logger()
    
    def _setup_task_logger(self):
        """设置任务专用日志"""
        try:
            # 创建日志目录
            os.makedirs(self.log_path, exist_ok=True)
            
            # 任务日志文件名
            timestamp = datetime.now().strftime('%Y%m%d')
            log_filename = f"{self.task_name}_{timestamp}.log"
            log_filepath = os.path.join(self.log_path, log_filename)
            
            # 创建文件处理器
            file_handler = logging.FileHandler(log_filepath, encoding='utf-8')
            file_handler.setLevel(logging.DEBUG)
            
            # 设置格式
            formatter = logging.Formatter(
                '%(asctime)s - %(levelname)s - %(message)s'
            )
            file_handler.setFormatter(formatter)
            
            # 添加处理器
            self.logger.addHandler(file_handler)
            self.logger.setLevel(logging.DEBUG)
            
        except Exception as e:
            logging.error(f"Failed to setup task logger for {self.task_name}: {str(e)}")
    
    def info(self, message: str):
        """记录信息日志"""
        self.logger.info(f"[{self.task_name}] {message}")
    
    def error(self, message: str):
        """记录错误日志"""
        self.logger.error(f"[{self.task_name}] {message}")
    
    def warning(self, message: str):
        """记录警告日志"""
        self.logger.warning(f"[{self.task_name}] {message}")
    
    def debug(self, message: str):
        """记录调试日志"""
        self.logger.debug(f"[{self.task_name}] {message}")


class PerformanceLogger:
    """性能日志器"""
    
    def __init__(self, operation_name: str):
        """
        初始化性能日志器
        
        Args:
            operation_name: 操作名称
        """
        self.operation_name = operation_name
        self.start_time = None
        self.logger = logging.getLogger("performance")
    
    def start(self):
        """开始计时"""
        self.start_time = datetime.now()
        self.logger.info(f"Started: {self.operation_name}")
    
    def end(self, additional_info: str = ""):
        """结束计时并记录"""
        if self.start_time:
            end_time = datetime.now()
            duration = (end_time - self.start_time).total_seconds()
            
            message = f"Completed: {self.operation_name} in {duration:.2f}s"
            if additional_info:
                message += f" - {additional_info}"
            
            self.logger.info(message)
            return duration
        return 0
    
    def __enter__(self):
        """上下文管理器入口"""
        self.start()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        if exc_type:
            self.logger.error(f"Failed: {self.operation_name} - {exc_val}")
        else:
            self.end()


def get_logger(name: str) -> logging.Logger:
    """
    获取指定名称的日志器
    
    Args:
        name: 日志器名称
        
    Returns:
        logging.Logger: 日志器实例
    """
    return logging.getLogger(name)


def log_function_call(func):
    """
    装饰器：记录函数调用
    
    Args:
        func: 要装饰的函数
        
    Returns:
        装饰后的函数
    """
    def wrapper(*args, **kwargs):
        logger = logging.getLogger(func.__module__)
        logger.debug(f"Calling function: {func.__name__}")
        
        try:
            result = func(*args, **kwargs)
            logger.debug(f"Function {func.__name__} completed successfully")
            return result
        except Exception as e:
            logger.error(f"Function {func.__name__} failed: {str(e)}")
            raise
    
    return wrapper


def cleanup_old_logs(log_path: str = "logs", keep_days: int = 30):
    """
    清理旧日志文件
    
    Args:
        log_path: 日志目录路径
        keep_days: 保留天数
    """
    try:
        if not os.path.exists(log_path):
            return
        
        cutoff_time = datetime.now().timestamp() - (keep_days * 24 * 60 * 60)
        cleaned_count = 0
        
        for log_file in Path(log_path).glob("*.log*"):
            if log_file.stat().st_mtime < cutoff_time:
                try:
                    log_file.unlink()
                    cleaned_count += 1
                except Exception as e:
                    logging.warning(f"Failed to delete log file {log_file}: {str(e)}")
        
        if cleaned_count > 0:
            logging.info(f"Cleaned up {cleaned_count} old log files")
    
    except Exception as e:
        logging.error(f"Failed to cleanup old logs: {str(e)}")


# 导出主要函数和类
__all__ = [
    'setup_logging',
    'TaskLogger',
    'PerformanceLogger',
    'get_logger',
    'log_function_call',
    'cleanup_old_logs'
]
