"""
配置管理模块
负责加载和管理系统配置
"""

import os
import yaml
from typing import Any, Dict, Optional
from dotenv import load_dotenv
import logging

logger = logging.getLogger(__name__)


class Config:
    """配置管理类"""
    
    def __init__(self, config_path: str = None):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径
        """
        self._config = {}
        self._env_config = {}
        
        # 加载环境变量
        self._load_env_variables()
        
        # 加载YAML配置文件
        if config_path:
            self._load_yaml_config(config_path)
        
        # 合并配置
        self._merge_configs()
    
    def _load_env_variables(self):
        """加载环境变量"""
        try:
            # 查找.env文件
            env_paths = [
                '.env',
                'config/.env',
                os.path.join(os.path.dirname(__file__), '..', '..', 'config', '.env')
            ]
            
            for env_path in env_paths:
                if os.path.exists(env_path):
                    load_dotenv(env_path)
                    logger.info(f"Loaded environment variables from {env_path}")
                    break
            
            # 映射环境变量到配置结构
            self._env_config = {
                'web': {
                    'target_url': os.getenv('TARGET_URL'),
                },
                'credentials': {
                    'username': os.getenv('USERNAME'),
                    'password': os.getenv('PASSWORD'),
                },
                'output': {
                    'base_path': os.getenv('OUTPUT_PATH', 'output'),
                },
                'chat': {
                    'wechat': {
                        'webhook_url': os.getenv('WECHAT_WEBHOOK_URL'),
                        'group_id': os.getenv('WECHAT_GROUP_ID'),
                    },
                    'dingtalk': {
                        'webhook_url': os.getenv('DINGTALK_WEBHOOK_URL'),
                        'secret': os.getenv('DINGTALK_SECRET'),
                    },
                    'feishu': {
                        'webhook_url': os.getenv('FEISHU_WEBHOOK_URL'),
                    },
                    'custom': {
                        'webhook_url': os.getenv('CUSTOM_WEBHOOK_URL'),
                    }
                },
                'network_transfer': {
                    'ftp': {
                        'host': os.getenv('FTP_HOST'),
                        'port': int(os.getenv('FTP_PORT', 21)),
                        'username': os.getenv('FTP_USERNAME'),
                        'password': os.getenv('FTP_PASSWORD'),
                        'remote_path': os.getenv('FTP_REMOTE_PATH', '/upload'),
                    },
                    'sftp': {
                        'host': os.getenv('SFTP_HOST'),
                        'port': int(os.getenv('SFTP_PORT', 22)),
                        'username': os.getenv('SFTP_USERNAME'),
                        'password': os.getenv('SFTP_PASSWORD'),
                        'remote_path': os.getenv('SFTP_REMOTE_PATH', '/upload'),
                    }
                },
                'security': {
                    'encryption': {
                        'key': os.getenv('ENCRYPTION_KEY'),
                    }
                },
                'debug': os.getenv('DEBUG', 'false').lower() == 'true'
            }
            
        except Exception as e:
            logger.warning(f"Failed to load environment variables: {str(e)}")
    
    def _load_yaml_config(self, config_path: str):
        """加载YAML配置文件"""
        try:
            if not os.path.exists(config_path):
                logger.warning(f"Config file not found: {config_path}")
                return
            
            with open(config_path, 'r', encoding='utf-8') as file:
                self._config = yaml.safe_load(file) or {}
            
            logger.info(f"Loaded configuration from {config_path}")
            
        except Exception as e:
            logger.error(f"Failed to load config file {config_path}: {str(e)}")
            self._config = {}
    
    def _merge_configs(self):
        """合并配置，环境变量优先级更高"""
        def merge_dict(base: dict, override: dict) -> dict:
            """递归合并字典"""
            result = base.copy()
            
            for key, value in override.items():
                if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                    result[key] = merge_dict(result[key], value)
                elif value is not None:  # 只有非None值才覆盖
                    result[key] = value
            
            return result
        
        # 环境变量覆盖YAML配置
        self._config = merge_dict(self._config, self._env_config)
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值，支持点号分隔的嵌套键
        
        Args:
            key: 配置键，支持 'section.subsection.key' 格式
            default: 默认值
            
        Returns:
            配置值
        """
        try:
            keys = key.split('.')
            value = self._config
            
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return default
            
            return value
            
        except Exception:
            return default
    
    def set(self, key: str, value: Any):
        """
        设置配置值
        
        Args:
            key: 配置键
            value: 配置值
        """
        try:
            keys = key.split('.')
            config = self._config
            
            # 创建嵌套字典结构
            for k in keys[:-1]:
                if k not in config:
                    config[k] = {}
                config = config[k]
            
            config[keys[-1]] = value
            
        except Exception as e:
            logger.error(f"Failed to set config {key}: {str(e)}")
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """
        获取配置段
        
        Args:
            section: 配置段名称
            
        Returns:
            配置段字典
        """
        return self.get(section, {})
    
    def has(self, key: str) -> bool:
        """
        检查配置键是否存在
        
        Args:
            key: 配置键
            
        Returns:
            bool: 是否存在
        """
        return self.get(key) is not None
    
    def validate(self) -> Dict[str, Any]:
        """
        验证配置完整性
        
        Returns:
            Dict: 验证结果
        """
        validation_result = {
            'is_valid': True,
            'errors': [],
            'warnings': []
        }
        
        # 必需配置检查
        required_configs = [
            'web.target_url',
            'credentials.username',
            'credentials.password'
        ]
        
        for config_key in required_configs:
            if not self.has(config_key):
                validation_result['is_valid'] = False
                validation_result['errors'].append(f"Missing required config: {config_key}")
        
        # 可选配置警告
        optional_configs = [
            'chat.wechat.webhook_url',
            'chat.dingtalk.webhook_url',
            'chat.feishu.webhook_url',
            'chat.custom.webhook_url'
        ]
        
        chat_configured = any(self.has(config_key) for config_key in optional_configs)
        if not chat_configured:
            validation_result['warnings'].append("No chat integration configured")
        
        # 路径检查
        output_path = self.get('output.base_path', 'output')
        if not os.path.exists(output_path):
            try:
                os.makedirs(output_path, exist_ok=True)
                validation_result['warnings'].append(f"Created output directory: {output_path}")
            except Exception as e:
                validation_result['errors'].append(f"Cannot create output directory {output_path}: {str(e)}")
                validation_result['is_valid'] = False
        
        return validation_result
    
    def to_dict(self) -> Dict[str, Any]:
        """
        返回完整配置字典
        
        Returns:
            Dict: 配置字典
        """
        return self._config.copy()
    
    def save_to_file(self, file_path: str):
        """
        保存配置到文件
        
        Args:
            file_path: 文件路径
        """
        try:
            # 创建目录
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # 保存配置（排除敏感信息）
            safe_config = self._config.copy()
            
            # 移除敏感信息
            if 'credentials' in safe_config:
                safe_config['credentials'] = {k: '***' for k in safe_config['credentials']}
            
            with open(file_path, 'w', encoding='utf-8') as file:
                yaml.dump(safe_config, file, default_flow_style=False, allow_unicode=True)
            
            logger.info(f"Configuration saved to {file_path}")
            
        except Exception as e:
            logger.error(f"Failed to save config to {file_path}: {str(e)}")
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"Config(sections={list(self._config.keys())})"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return f"Config({self._config})"
