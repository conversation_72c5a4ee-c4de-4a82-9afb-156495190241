"""
Web Data Automation 打包配置
"""

from setuptools import setup, find_packages
import os

# 读取README文件
def read_readme():
    with open("README.md", "r", encoding="utf-8") as fh:
        return fh.read()

# 读取requirements文件
def read_requirements():
    with open("requirements.txt", "r", encoding="utf-8") as fh:
        return [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="web-data-automation",
    version="1.0.0",
    author="Web Data Automation Team",
    author_email="<EMAIL>",
    description="自动化网页数据获取和推送系统",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/your-username/web-data-automation",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Internet :: WWW/HTTP :: Browsers",
        "Topic :: Office/Business",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=8.1.1",
            "pytest-cov>=4.0.0",
            "black>=24.3.0",
            "flake8>=7.0.0",
        ],
        "gui": [
            "PyQt5>=5.15.10",
            "kivy>=2.3.0",
        ],
        "advanced": [
            "pillow>=10.2.0",
            "opencv-python>=********",
            "pytesseract>=0.3.10",
        ],
    },
    entry_points={
        "console_scripts": [
            "web-data-automation=main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.yaml", "*.yml", "*.json", "*.txt"],
    },
    zip_safe=False,
)
