"""
Web Data Automation 使用示例
演示如何使用各个模块
"""

import os
import sys
from datetime import datetime

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.config.settings import Config
from src.core.web_scraper import WebScraper
from src.core.data_processor import DataProcessor
from src.core.file_manager import FileManager
from src.integrations.chat_sender import ChatSenderFactory
from src.scheduler.task_scheduler import TaskScheduler
from src.utils.logger import setup_logging, TaskLogger, PerformanceLogger


def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    # 1. 加载配置
    config = Config('config/config.yaml')
    
    # 2. 设置日志
    setup_logging(config)
    
    # 3. 验证配置
    validation = config.validate()
    print(f"配置验证: {'✅ 通过' if validation['is_valid'] else '❌ 失败'}")
    
    if validation['errors']:
        print(f"错误: {validation['errors']}")
    if validation['warnings']:
        print(f"警告: {validation['warnings']}")


def example_web_scraping():
    """网页爬取示例"""
    print("\n=== 网页爬取示例 ===")
    
    config = Config('config/config.yaml')
    
    # 使用上下文管理器确保资源正确释放
    with WebScraper(config) as scraper:
        # 登录
        if scraper.login():
            print("✅ 登录成功")
            
            # 导航到目标页面
            if scraper.navigate_to_target_page():
                print("✅ 导航成功")
                
                # 应用筛选条件
                if scraper.apply_filters():
                    print("✅ 筛选成功")
                    
                    # 获取数据
                    html_content = scraper.fetch_data()
                    if html_content:
                        print(f"✅ 数据获取成功，长度: {len(html_content)}")
                        return html_content
                    else:
                        print("❌ 数据获取失败")
                else:
                    print("❌ 筛选失败")
            else:
                print("❌ 导航失败")
        else:
            print("❌ 登录失败")
    
    return None


def example_data_processing():
    """数据处理示例"""
    print("\n=== 数据处理示例 ===")
    
    config = Config('config/config.yaml')
    processor = DataProcessor(config)
    
    # 模拟HTML数据
    sample_html = """
    <table>
        <tr><th>姓名</th><th>年龄</th><th>城市</th></tr>
        <tr><td>张三</td><td>25</td><td>北京</td></tr>
        <tr><td>李四</td><td>30</td><td>上海</td></tr>
        <tr><td>王五</td><td>28</td><td>广州</td></tr>
    </table>
    """
    
    # 处理数据
    df, validation_result = processor.process_data(sample_html)
    
    if validation_result['is_valid']:
        print("✅ 数据处理成功")
        print(f"数据行数: {len(df)}")
        print(f"数据列数: {len(df.columns)}")
        print("数据预览:")
        print(df.head())
        return df
    else:
        print("❌ 数据处理失败")
        print(f"错误: {validation_result['errors']}")
    
    return None


def example_file_management():
    """文件管理示例"""
    print("\n=== 文件管理示例 ===")
    
    config = Config('config/config.yaml')
    file_manager = FileManager(config)
    
    # 创建示例数据
    import pandas as pd
    df = pd.DataFrame({
        '姓名': ['张三', '李四', '王五'],
        '年龄': [25, 30, 28],
        '城市': ['北京', '上海', '广州']
    })
    
    # 保存文件
    file_paths = file_manager.save_data(df, 'example_data')
    
    if file_paths:
        print("✅ 文件保存成功")
        for path in file_paths:
            print(f"文件: {path}")
            
            # 获取文件信息
            file_info = file_manager.get_file_info(path)
            print(f"  大小: {file_info.get('size_mb', 0)} MB")
            print(f"  创建时间: {file_info.get('created', 'Unknown')}")
        
        return file_paths
    else:
        print("❌ 文件保存失败")
    
    return []


def example_chat_integration():
    """聊天软件集成示例"""
    print("\n=== 聊天软件集成示例 ===")
    
    config = Config('config/config.yaml')
    chat_sender = ChatSenderFactory.create_sender(config)
    
    if chat_sender:
        print("✅ 聊天发送器创建成功")
        
        # 发送测试消息
        test_message = f"🔧 系统测试消息 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        if chat_sender.send_message(test_message):
            print("✅ 消息发送成功")
        else:
            print("❌ 消息发送失败")
    else:
        print("❌ 未配置聊天软件")


def example_task_scheduling():
    """定时任务示例"""
    print("\n=== 定时任务示例 ===")
    
    config = Config('config/config.yaml')
    scheduler = TaskScheduler(config)
    
    # 定义测试任务
    def test_task():
        print(f"⏰ 定时任务执行 - {datetime.now()}")
        return True
    
    # 注册任务（每分钟执行一次，仅用于演示）
    scheduler.register_job(
        name="test_job",
        func=test_task,
        schedule_expr="every 1 minutes",
        enabled=True
    )
    
    # 获取任务状态
    job_status = scheduler.get_job_status()
    print(f"已注册任务数: {len(job_status)}")
    
    for job in job_status:
        print(f"任务: {job['name']}, 调度: {job['schedule']}")
    
    # 手动运行任务
    if scheduler.run_job_now("test_job"):
        print("✅ 手动执行任务成功")
    else:
        print("❌ 手动执行任务失败")


def example_performance_logging():
    """性能日志示例"""
    print("\n=== 性能日志示例 ===")
    
    # 使用性能日志器
    with PerformanceLogger("数据处理操作") as perf:
        # 模拟耗时操作
        import time
        time.sleep(1)
        print("✅ 模拟操作完成")
    
    # 使用任务日志器
    task_logger = TaskLogger("example_task")
    task_logger.info("任务开始执行")
    task_logger.info("任务执行中...")
    task_logger.info("任务执行完成")


def example_complete_workflow():
    """完整工作流示例"""
    print("\n=== 完整工作流示例 ===")
    
    try:
        # 1. 配置初始化
        config = Config('config/config.yaml')
        setup_logging(config)
        
        # 2. 数据处理（使用模拟数据）
        processor = DataProcessor(config)
        sample_html = """
        <table>
            <tr><th>产品</th><th>销量</th><th>收入</th></tr>
            <tr><td>产品A</td><td>100</td><td>10000</td></tr>
            <tr><td>产品B</td><td>150</td><td>15000</td></tr>
        </table>
        """
        
        df, validation = processor.process_data(sample_html)
        
        if not validation['is_valid']:
            print("❌ 数据验证失败")
            return
        
        # 3. 文件保存
        file_manager = FileManager(config)
        file_paths = file_manager.save_data(df, 'workflow_example')
        
        if not file_paths:
            print("❌ 文件保存失败")
            return
        
        # 4. 发送通知
        chat_sender = ChatSenderFactory.create_sender(config)
        if chat_sender:
            for file_path in file_paths:
                chat_sender.send_file(file_path, "工作流示例数据")
        
        print("✅ 完整工作流执行成功")
        
    except Exception as e:
        print(f"❌ 工作流执行失败: {str(e)}")


def main():
    """主函数"""
    print("Web Data Automation 使用示例")
    print("=" * 50)
    
    # 检查配置文件
    config_path = 'config/config.yaml'
    if not os.path.exists(config_path):
        print(f"❌ 配置文件不存在: {config_path}")
        print("请先创建配置文件")
        return
    
    try:
        # 运行各种示例
        example_basic_usage()
        example_data_processing()
        example_file_management()
        example_chat_integration()
        example_task_scheduling()
        example_performance_logging()
        example_complete_workflow()
        
        print("\n" + "=" * 50)
        print("✅ 所有示例执行完成")
        
    except Exception as e:
        print(f"\n❌ 示例执行失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
