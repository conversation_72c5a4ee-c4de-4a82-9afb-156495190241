# Web Data Automation 项目依赖

# 核心依赖
selenium==4.18.1
webdriver-manager==4.0.1
requests==2.31.0
pandas==2.2.1
openpyxl==3.1.2
python-dotenv==1.0.1
schedule==1.2.1
PyYAML==6.0.1

# 数据处理
numpy==1.26.4
lxml==5.1.0
beautifulsoup4==4.12.3

# 网络和安全
urllib3==2.2.1
certifi==2024.2.2
cryptography==42.0.5

# 文件处理
zipfile36==0.1.3
pathlib2==2.3.7

# 日志和监控
colorlog==6.8.2

# 打包工具
pyinstaller==6.5.0
auto-py-to-exe==2.43.1

# 开发和测试工具
pytest==8.1.1
pytest-cov==4.0.0
black==24.3.0
flake8==7.0.0

# 可选依赖 - 数据库支持
# sqlalchemy==2.0.29
# pymongo==4.6.2

# 可选依赖 - 高级功能
# pillow==10.2.0  # 图像处理
# opencv-python==********  # 计算机视觉
# pytesseract==0.3.10  # OCR文字识别

# 可选依赖 - GUI界面
# tkinter  # 通常随Python安装
# PyQt5==5.15.10
# kivy==2.3.0

# 可选依赖 - 邮件发送
# smtplib  # Python标准库
# email-validator==2.1.1

# 可选依赖 - 更多聊天软件支持
# wechatpy==1.8.18  # 微信开发
# dingtalk-sdk==1.0.1  # 钉钉SDK

# 可选依赖 - 高级网络功能
# aiohttp==3.9.3  # 异步HTTP
# websockets==12.0  # WebSocket支持
# paramiko==3.4.0  # SSH/SFTP

# 可选依赖 - 数据可视化
# matplotlib==3.8.3
# plotly==5.19.0
# seaborn==0.13.2

# 可选依赖 - 机器学习
# scikit-learn==1.4.1
# tensorflow==2.16.1
# torch==2.2.1
