"""
定时任务调度器模块
基于schedule库实现灵活的任务调度
"""

import time
import schedule
import threading
from datetime import datetime
from typing import Callable, Dict, Any, List
import logging

logger = logging.getLogger(__name__)


class TaskScheduler:
    """任务调度器"""
    
    def __init__(self, config):
        """
        初始化任务调度器
        
        Args:
            config: 配置对象
        """
        self.config = config
        self.scheduler_config = config.get('scheduler', {})
        self.jobs = {}
        self.running = False
        self.scheduler_thread = None
        
        # 设置时区
        self.timezone = self.scheduler_config.get('timezone', 'Asia/Shanghai')
        
        logger.info("Task scheduler initialized")
    
    def register_job(self, name: str, func: Callable, schedule_expr: str, 
                    enabled: bool = True, **kwargs):
        """
        注册定时任务
        
        Args:
            name: 任务名称
            func: 要执行的函数
            schedule_expr: 调度表达式 (cron格式或简单格式)
            enabled: 是否启用
            **kwargs: 传递给函数的参数
        """
        try:
            if not enabled:
                logger.info(f"Job '{name}' is disabled, skipping registration")
                return
            
            # 解析调度表达式并创建任务
            job = self._create_schedule_job(func, schedule_expr, **kwargs)
            
            if job:
                self.jobs[name] = {
                    'job': job,
                    'func': func,
                    'schedule': schedule_expr,
                    'enabled': enabled,
                    'last_run': None,
                    'next_run': None,
                    'run_count': 0,
                    'kwargs': kwargs
                }
                
                logger.info(f"Job '{name}' registered with schedule: {schedule_expr}")
            else:
                logger.error(f"Failed to create job '{name}' with schedule: {schedule_expr}")
                
        except Exception as e:
            logger.error(f"Failed to register job '{name}': {str(e)}")
    
    def _create_schedule_job(self, func: Callable, schedule_expr: str, **kwargs):
        """
        根据调度表达式创建schedule任务
        
        Args:
            func: 要执行的函数
            schedule_expr: 调度表达式
            **kwargs: 函数参数
            
        Returns:
            schedule.Job: 创建的任务对象
        """
        try:
            # 包装函数以添加日志和错误处理
            def wrapped_func():
                job_name = None
                for name, job_info in self.jobs.items():
                    if job_info['func'] == func:
                        job_name = name
                        break
                
                try:
                    logger.info(f"Starting scheduled job: {job_name or 'unknown'}")
                    start_time = datetime.now()
                    
                    # 执行函数
                    result = func(**kwargs)
                    
                    end_time = datetime.now()
                    duration = (end_time - start_time).total_seconds()
                    
                    # 更新任务信息
                    if job_name and job_name in self.jobs:
                        self.jobs[job_name]['last_run'] = start_time
                        self.jobs[job_name]['run_count'] += 1
                    
                    logger.info(f"Job '{job_name}' completed in {duration:.2f} seconds")
                    return result
                    
                except Exception as e:
                    logger.error(f"Job '{job_name}' failed: {str(e)}")
                    raise
            
            # 解析调度表达式
            if self._is_cron_expression(schedule_expr):
                return self._parse_cron_expression(wrapped_func, schedule_expr)
            else:
                return self._parse_simple_expression(wrapped_func, schedule_expr)
                
        except Exception as e:
            logger.error(f"Failed to create schedule job: {str(e)}")
            return None
    
    def _is_cron_expression(self, expr: str) -> bool:
        """检查是否为cron表达式"""
        # 简单检查：cron表达式通常有5个字段
        parts = expr.strip().split()
        return len(parts) == 5
    
    def _parse_cron_expression(self, func: Callable, cron_expr: str):
        """
        解析cron表达式
        格式: 分 时 日 月 周
        """
        try:
            parts = cron_expr.strip().split()
            if len(parts) != 5:
                raise ValueError("Invalid cron expression format")
            
            minute, hour, day, month, weekday = parts
            
            # 转换为schedule库的格式
            if minute == '*' and hour != '*':
                # 每小时的指定分钟
                return schedule.every().hour.at(f":{minute}").do(func)
            elif minute != '*' and hour != '*':
                # 每天的指定时间
                time_str = f"{hour.zfill(2)}:{minute.zfill(2)}"
                if weekday != '*':
                    # 每周的指定时间
                    weekday_map = {
                        '0': 'sunday', '1': 'monday', '2': 'tuesday',
                        '3': 'wednesday', '4': 'thursday', '5': 'friday', '6': 'saturday'
                    }
                    if weekday in weekday_map:
                        return getattr(schedule.every(), weekday_map[weekday]).at(time_str).do(func)
                else:
                    return schedule.every().day.at(time_str).do(func)
            
            # 默认情况
            logger.warning(f"Unsupported cron expression: {cron_expr}, using daily at 09:00")
            return schedule.every().day.at("09:00").do(func)
            
        except Exception as e:
            logger.error(f"Failed to parse cron expression '{cron_expr}': {str(e)}")
            return None
    
    def _parse_simple_expression(self, func: Callable, expr: str):
        """
        解析简单调度表达式
        支持格式：
        - "every 30 minutes"
        - "every hour"
        - "every day at 09:00"
        - "every monday at 10:00"
        """
        try:
            expr = expr.lower().strip()
            
            if "every" not in expr:
                raise ValueError("Expression must start with 'every'")
            
            # 移除 "every" 前缀
            expr = expr.replace("every", "").strip()
            
            if "minutes" in expr:
                # 每N分钟
                minutes = int(expr.split()[0])
                return schedule.every(minutes).minutes.do(func)
            
            elif "hour" in expr and "at" not in expr:
                # 每小时
                return schedule.every().hour.do(func)
            
            elif "day at" in expr:
                # 每天指定时间
                time_str = expr.split("at")[1].strip()
                return schedule.every().day.at(time_str).do(func)
            
            elif any(day in expr for day in ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']):
                # 每周指定日期
                for day in ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']:
                    if day in expr:
                        if "at" in expr:
                            time_str = expr.split("at")[1].strip()
                            return getattr(schedule.every(), day).at(time_str).do(func)
                        else:
                            return getattr(schedule.every(), day).do(func)
            
            # 默认情况
            logger.warning(f"Unsupported expression: {expr}, using daily at 09:00")
            return schedule.every().day.at("09:00").do(func)
            
        except Exception as e:
            logger.error(f"Failed to parse simple expression '{expr}': {str(e)}")
            return None
    
    def start(self):
        """启动调度器"""
        try:
            if self.running:
                logger.warning("Scheduler is already running")
                return
            
            if not self.jobs:
                logger.warning("No jobs registered, scheduler will not start")
                return
            
            self.running = True
            
            # 在单独线程中运行调度器
            self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
            self.scheduler_thread.start()
            
            logger.info(f"Task scheduler started with {len(self.jobs)} jobs")
            
        except Exception as e:
            logger.error(f"Failed to start scheduler: {str(e)}")
            self.running = False
    
    def stop(self):
        """停止调度器"""
        try:
            self.running = False
            
            if self.scheduler_thread and self.scheduler_thread.is_alive():
                self.scheduler_thread.join(timeout=5)
            
            # 清除所有任务
            schedule.clear()
            
            logger.info("Task scheduler stopped")
            
        except Exception as e:
            logger.error(f"Failed to stop scheduler: {str(e)}")
    
    def _run_scheduler(self):
        """运行调度器主循环"""
        logger.info("Scheduler main loop started")
        
        while self.running:
            try:
                # 运行待执行的任务
                schedule.run_pending()
                
                # 更新下次运行时间
                self._update_next_run_times()
                
                # 短暂休眠
                time.sleep(1)
                
            except Exception as e:
                logger.error(f"Error in scheduler main loop: {str(e)}")
                time.sleep(5)  # 出错时等待更长时间
        
        logger.info("Scheduler main loop ended")
    
    def _update_next_run_times(self):
        """更新任务的下次运行时间"""
        try:
            for name, job_info in self.jobs.items():
                job = job_info['job']
                if hasattr(job, 'next_run'):
                    job_info['next_run'] = job.next_run
                    
        except Exception as e:
            logger.error(f"Failed to update next run times: {str(e)}")
    
    def get_job_status(self) -> List[Dict[str, Any]]:
        """
        获取所有任务状态
        
        Returns:
            List[Dict]: 任务状态列表
        """
        status_list = []
        
        try:
            for name, job_info in self.jobs.items():
                status = {
                    'name': name,
                    'schedule': job_info['schedule'],
                    'enabled': job_info['enabled'],
                    'last_run': job_info['last_run'],
                    'next_run': job_info['next_run'],
                    'run_count': job_info['run_count']
                }
                status_list.append(status)
            
            return status_list
            
        except Exception as e:
            logger.error(f"Failed to get job status: {str(e)}")
            return []
    
    def enable_job(self, name: str):
        """启用任务"""
        if name in self.jobs:
            self.jobs[name]['enabled'] = True
            logger.info(f"Job '{name}' enabled")
        else:
            logger.warning(f"Job '{name}' not found")
    
    def disable_job(self, name: str):
        """禁用任务"""
        if name in self.jobs:
            self.jobs[name]['enabled'] = False
            # 从schedule中移除
            job = self.jobs[name]['job']
            schedule.cancel_job(job)
            logger.info(f"Job '{name}' disabled")
        else:
            logger.warning(f"Job '{name}' not found")
    
    def run_job_now(self, name: str) -> bool:
        """立即运行指定任务"""
        try:
            if name not in self.jobs:
                logger.error(f"Job '{name}' not found")
                return False
            
            job_info = self.jobs[name]
            func = job_info['func']
            kwargs = job_info.get('kwargs', {})
            
            logger.info(f"Running job '{name}' manually")
            result = func(**kwargs)
            
            # 更新运行统计
            job_info['last_run'] = datetime.now()
            job_info['run_count'] += 1
            
            logger.info(f"Job '{name}' completed manually")
            return True
            
        except Exception as e:
            logger.error(f"Failed to run job '{name}': {str(e)}")
            return False
    
    def is_running(self) -> bool:
        """检查调度器是否运行中"""
        return self.running
    
    def __del__(self):
        """析构函数，确保调度器停止"""
        if self.running:
            self.stop()
