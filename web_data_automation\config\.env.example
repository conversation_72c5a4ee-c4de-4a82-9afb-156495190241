# Web Data Automation 环境变量配置文件
# 复制此文件为 .env 并填入实际值

# 网页登录凭据
TARGET_URL=https://example.com/login
USERNAME=your_username
PASSWORD=your_password

# 输出路径配置
OUTPUT_PATH=output
LOG_PATH=logs

# 聊天软件配置
# 企业微信
WECHAT_WEBHOOK_URL=
WECHAT_GROUP_ID=

# 钉钉
DINGTALK_WEBHOOK_URL=
DINGTALK_SECRET=

# 飞书
FEISHU_WEBHOOK_URL=

# 自定义Webhook
CUSTOM_WEBHOOK_URL=

# 内网文件传输配置
FTP_HOST=
FTP_PORT=21
FTP_USERNAME=
FTP_PASSWORD=
FTP_REMOTE_PATH=/upload

SFTP_HOST=
SFTP_PORT=22
SFTP_USERNAME=
SFTP_PASSWORD=
SFTP_REMOTE_PATH=/upload

# 邮件告警配置
EMAIL_SMTP_SERVER=
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=
EMAIL_PASSWORD=
EMAIL_RECIPIENTS=<EMAIL>,<EMAIL>

# 安全配置
ENCRYPTION_KEY=your_encryption_key_here

# 数据库配置（可选）
DATABASE_URL=sqlite:///automation.db

# API密钥（如果需要）
API_KEY=your_api_key_here
API_SECRET=your_api_secret_here

# 代理配置（如果需要）
HTTP_PROXY=
HTTPS_PROXY=

# 调试模式
DEBUG=false

# 其他自定义配置
CUSTOM_CONFIG_1=
CUSTOM_CONFIG_2=
