# Web Data Automation 配置文件

# 基本设置
app:
  name: "Web Data Automation"
  version: "1.0.0"
  debug: false
  
# 网页登录配置
web:
  target_url: "https://example.com/login"
  login:
    username_field: "username"
    password_field: "password"
    login_button: "//button[@type='submit']"
    success_indicator: "//div[@class='dashboard']"
  
  # 目标页面配置
  target_page:
    url: "https://example.com/data"
    navigation_link: "//a[contains(text(), '数据页面')]"
    
  # 筛选条件配置
  filters:
    date_range:
      start_date_field: "start-date"
      end_date_field: "end-date"
      default_start: "2024-01-01"
      default_end: "today"
    
    category:
      select_field: "category-select"
      default_value: "全部"
    
    filter_button: "//button[contains(text(), '筛选')]"

# Selenium配置
selenium:
  browser: "chrome"
  headless: false  # 调试时设为false，生产环境设为true
  window_size: "1920,1080"
  timeout: 30
  implicit_wait: 10
  
  # Chrome选项
  chrome_options:
    - "--no-sandbox"
    - "--disable-dev-shm-usage"
    - "--disable-gpu"
    - "--start-maximized"
    # - "--headless"  # 生产环境取消注释

# Requests配置
requests:
  timeout: 30
  max_retries: 3
  retry_delay: 5
  
  # 请求头配置
  headers:
    User-Agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    Accept: "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8"
    Accept-Language: "zh-CN,zh;q=0.9,en;q=0.8"
    Accept-Encoding: "gzip, deflate, br"
    Connection: "keep-alive"

# 数据处理配置
data_processing:
  # 数据清洗规则
  cleaning:
    remove_empty_rows: true
    remove_empty_columns: true
    strip_whitespace: true
    
  # 数据转换规则
  transformation:
    date_columns: []  # 需要转换为日期格式的列名
    numeric_columns: []  # 需要转换为数字格式的列名
    
  # 数据验证规则
  validation:
    required_columns: []  # 必须存在的列名
    min_rows: 1  # 最少行数
    max_rows: 10000  # 最多行数

# 文件输出配置
output:
  base_path: "output"
  filename_template: "data_{timestamp}"
  formats:
    - "xlsx"
    - "csv"
  
  # Excel特定配置
  excel:
    sheet_name: "数据"
    include_index: false
    
  # 文件归档配置
  archive:
    enabled: true
    keep_days: 30
    archive_path: "output/archive"

# 聊天软件集成配置
chat:
  # 企业微信配置
  wechat:
    enabled: false
    webhook_url: ""
    group_id: ""
    
  # 钉钉配置
  dingtalk:
    enabled: false
    webhook_url: ""
    secret: ""
    
  # 飞书配置
  feishu:
    enabled: false
    webhook_url: ""
    
  # 自定义Webhook
  custom:
    enabled: true
    webhook_url: ""
    method: "POST"
    headers: {}

# 内网文件传输配置
network_transfer:
  enabled: false
  method: "ftp"  # ftp, sftp, smb, http
  
  ftp:
    host: ""
    port: 21
    username: ""
    password: ""
    remote_path: "/upload"
    
  sftp:
    host: ""
    port: 22
    username: ""
    password: ""
    remote_path: "/upload"

# 定时任务配置
scheduler:
  enabled: true
  timezone: "Asia/Shanghai"
  
  # 任务列表
  jobs:
    - name: "daily_data_fetch"
      schedule: "0 9 * * *"  # 每天上午9点
      enabled: true
      
    - name: "weekly_report"
      schedule: "0 18 * * 5"  # 每周五下午6点
      enabled: false

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  # 文件日志
  file:
    enabled: true
    path: "logs"
    filename: "automation.log"
    max_size: "10MB"
    backup_count: 5
    
  # 控制台日志
  console:
    enabled: true
    level: "INFO"

# 监控和告警配置
monitoring:
  enabled: true
  
  # 性能监控
  performance:
    track_execution_time: true
    track_memory_usage: true
    
  # 错误告警
  alerts:
    email:
      enabled: false
      smtp_server: ""
      smtp_port: 587
      username: ""
      password: ""
      recipients: []
      
    webhook:
      enabled: false
      url: ""

# 安全配置
security:
  # 敏感信息加密
  encryption:
    enabled: false
    key: ""
    
  # 访问控制
  access_control:
    enabled: false
    allowed_ips: []
