"""
Web Scraper 模块
结合 Selenium 和 Requests 进行网页数据获取
"""

import time
import requests
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import Select
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)


class WebScraper:
    """Web数据爬取器"""
    
    def __init__(self, config):
        """
        初始化爬虫
        
        Args:
            config: 配置对象
        """
        self.config = config
        self.driver = None
        self.session = None
        self.cookies = None
        
    def setup_driver(self):
        """设置Selenium WebDriver"""
        try:
            chrome_options = Options()
            
            # 从配置文件加载Chrome选项
            for option in self.config.get('selenium.chrome_options', []):
                chrome_options.add_argument(option)
            
            # 设置窗口大小
            window_size = self.config.get('selenium.window_size', '1920,1080')
            chrome_options.add_argument(f'--window-size={window_size}')
            
            # 设置User-Agent
            user_agent = self.config.get('requests.headers.User-Agent')
            if user_agent:
                chrome_options.add_argument(f'user-agent={user_agent}')
            
            # 无头模式
            if self.config.get('selenium.headless', False):
                chrome_options.add_argument('--headless')
            
            # 设置WebDriver
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
            # 设置超时时间
            timeout = self.config.get('selenium.timeout', 30)
            self.driver.implicitly_wait(self.config.get('selenium.implicit_wait', 10))
            
            logger.info("WebDriver setup completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to setup WebDriver: {str(e)}")
            return False
    
    def login(self):
        """登录目标网站"""
        try:
            if not self.driver:
                if not self.setup_driver():
                    raise Exception("Failed to setup WebDriver")
            
            # 访问登录页面
            login_url = self.config.get('web.target_url')
            logger.info(f"Navigating to login page: {login_url}")
            self.driver.get(login_url)
            
            # 等待页面加载
            time.sleep(2)
            
            # 定位用户名输入框
            username_field = self.config.get('web.login.username_field', 'username')
            username_element = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.NAME, username_field))
            )
            
            # 定位密码输入框
            password_field = self.config.get('web.login.password_field', 'password')
            password_element = self.driver.find_element(By.NAME, password_field)
            
            # 输入登录信息
            username = self.config.get('credentials.username')
            password = self.config.get('credentials.password')
            
            username_element.clear()
            username_element.send_keys(username)
            password_element.clear()
            password_element.send_keys(password)
            
            # 点击登录按钮
            login_button_xpath = self.config.get('web.login.login_button', "//button[@type='submit']")
            login_button = self.driver.find_element(By.XPATH, login_button_xpath)
            login_button.click()
            
            # 等待登录成功
            success_indicator = self.config.get('web.login.success_indicator')
            if success_indicator:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.XPATH, success_indicator))
                )
            else:
                time.sleep(5)  # 默认等待5秒
            
            logger.info("Login successful")
            return True
            
        except TimeoutException:
            logger.error("Login timeout - page elements not found")
            return False
        except Exception as e:
            logger.error(f"Login failed: {str(e)}")
            return False
    
    def navigate_to_target_page(self):
        """导航到目标数据页面"""
        try:
            # 检查是否需要点击导航链接
            navigation_link = self.config.get('web.target_page.navigation_link')
            if navigation_link:
                link_element = WebDriverWait(self.driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, navigation_link))
                )
                link_element.click()
                time.sleep(3)
            
            # 或者直接访问目标URL
            target_url = self.config.get('web.target_page.url')
            if target_url:
                self.driver.get(target_url)
                time.sleep(3)
            
            logger.info("Successfully navigated to target page")
            return True
            
        except Exception as e:
            logger.error(f"Failed to navigate to target page: {str(e)}")
            return False
    
    def apply_filters(self):
        """应用筛选条件"""
        try:
            # 日期范围筛选
            date_config = self.config.get('web.filters.date_range', {})
            if date_config:
                start_date_field = date_config.get('start_date_field')
                end_date_field = date_config.get('end_date_field')
                
                if start_date_field and end_date_field:
                    # 设置开始日期
                    start_date = date_config.get('default_start', '2024-01-01')
                    if start_date == 'today':
                        start_date = datetime.now().strftime('%Y-%m-%d')
                    
                    start_element = self.driver.find_element(By.ID, start_date_field)
                    start_element.clear()
                    start_element.send_keys(start_date)
                    
                    # 设置结束日期
                    end_date = date_config.get('default_end', 'today')
                    if end_date == 'today':
                        end_date = datetime.now().strftime('%Y-%m-%d')
                    
                    end_element = self.driver.find_element(By.ID, end_date_field)
                    end_element.clear()
                    end_element.send_keys(end_date)
            
            # 类别筛选
            category_config = self.config.get('web.filters.category', {})
            if category_config:
                select_field = category_config.get('select_field')
                default_value = category_config.get('default_value', '全部')
                
                if select_field:
                    select_element = Select(self.driver.find_element(By.ID, select_field))
                    select_element.select_by_visible_text(default_value)
            
            # 点击筛选按钮
            filter_button_xpath = self.config.get('web.filters.filter_button')
            if filter_button_xpath:
                filter_button = self.driver.find_element(By.XPATH, filter_button_xpath)
                filter_button.click()
                time.sleep(3)  # 等待筛选结果加载
            
            logger.info("Filters applied successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to apply filters: {str(e)}")
            return False
    
    def get_cookies(self):
        """获取当前会话的cookies"""
        if self.driver:
            self.cookies = self.driver.get_cookies()
            return self.cookies
        return None
    
    def setup_requests_session(self):
        """设置Requests会话"""
        try:
            self.session = requests.Session()
            
            # 设置请求头
            headers = self.config.get('requests.headers', {})
            self.session.headers.update(headers)
            
            # 设置cookies
            if self.cookies:
                for cookie in self.cookies:
                    self.session.cookies.set(
                        name=cookie['name'],
                        value=cookie['value'],
                        domain=cookie.get('domain', ''),
                        path=cookie.get('path', '/')
                    )
            
            # 设置超时和重试
            timeout = self.config.get('requests.timeout', 30)
            self.session.timeout = timeout
            
            logger.info("Requests session setup completed")
            return True
            
        except Exception as e:
            logger.error(f"Failed to setup requests session: {str(e)}")
            return False
    
    def fetch_data(self, url=None):
        """使用Requests获取数据"""
        try:
            if not self.session:
                if not self.setup_requests_session():
                    raise Exception("Failed to setup requests session")
            
            # 使用当前页面URL或指定URL
            target_url = url or self.driver.current_url
            
            max_retries = self.config.get('requests.max_retries', 3)
            retry_delay = self.config.get('requests.retry_delay', 5)
            
            for attempt in range(max_retries):
                try:
                    response = self.session.get(target_url)
                    response.raise_for_status()
                    
                    logger.info(f"Data fetched successfully from {target_url}")
                    return response.text
                    
                except requests.exceptions.RequestException as e:
                    logger.warning(f"Attempt {attempt + 1} failed: {str(e)}")
                    if attempt < max_retries - 1:
                        time.sleep(retry_delay)
                    else:
                        raise
            
        except Exception as e:
            logger.error(f"Failed to fetch data: {str(e)}")
            return None
    
    def close(self):
        """关闭浏览器和会话"""
        try:
            if self.driver:
                self.driver.quit()
                self.driver = None
            
            if self.session:
                self.session.close()
                self.session = None
            
            logger.info("WebScraper closed successfully")
            
        except Exception as e:
            logger.error(f"Error closing WebScraper: {str(e)}")
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()
