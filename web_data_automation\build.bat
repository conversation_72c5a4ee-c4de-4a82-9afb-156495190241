@echo off
chcp 65001 >nul
echo ========================================
echo Web Data Automation 打包脚本
echo ========================================
echo.

REM 检查虚拟环境
if not exist "venv" (
    echo ❌ 虚拟环境不存在，请先运行 run.bat
    pause
    exit /b 1
)

REM 激活虚拟环境
echo 🔧 激活虚拟环境...
call venv\Scripts\activate.bat

REM 安装打包工具
echo 📦 安装打包工具...
pip install pyinstaller

REM 清理旧的构建文件
if exist "dist" rmdir /s /q dist
if exist "build" rmdir /s /q build
if exist "*.spec" del *.spec

REM 创建打包配置
echo 🔧 创建打包配置...
echo # -*- mode: python ; coding: utf-8 -*- > main.spec
echo. >> main.spec
echo block_cipher = None >> main.spec
echo. >> main.spec
echo a = Analysis( >> main.spec
echo     ['main.py'], >> main.spec
echo     pathex=[], >> main.spec
echo     binaries=[], >> main.spec
echo     datas=[ >> main.spec
echo         ('config', 'config'), >> main.spec
echo         ('src', 'src'), >> main.spec
echo     ], >> main.spec
echo     hiddenimports=[ >> main.spec
echo         'selenium', >> main.spec
echo         'webdriver_manager', >> main.spec
echo         'requests', >> main.spec
echo         'pandas', >> main.spec
echo         'openpyxl', >> main.spec
echo         'schedule', >> main.spec
echo         'yaml', >> main.spec
echo     ], >> main.spec
echo     hookspath=[], >> main.spec
echo     hooksconfig={}, >> main.spec
echo     runtime_hooks=[], >> main.spec
echo     excludes=[], >> main.spec
echo     win_no_prefer_redirects=False, >> main.spec
echo     win_private_assemblies=False, >> main.spec
echo     cipher=block_cipher, >> main.spec
echo     noarchive=False, >> main.spec
echo ^) >> main.spec
echo. >> main.spec
echo pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher^) >> main.spec
echo. >> main.spec
echo exe = EXE( >> main.spec
echo     pyz, >> main.spec
echo     a.scripts, >> main.spec
echo     a.binaries, >> main.spec
echo     a.zipfiles, >> main.spec
echo     a.datas, >> main.spec
echo     [], >> main.spec
echo     name='WebDataAutomation', >> main.spec
echo     debug=False, >> main.spec
echo     bootloader_ignore_signals=False, >> main.spec
echo     strip=False, >> main.spec
echo     upx=True, >> main.spec
echo     upx_exclude=[], >> main.spec
echo     runtime_tmpdir=None, >> main.spec
echo     console=True, >> main.spec
echo     disable_windowed_traceback=False, >> main.spec
echo     argv_emulation=False, >> main.spec
echo     target_arch=None, >> main.spec
echo     codesign_identity=None, >> main.spec
echo     entitlements_file=None, >> main.spec
echo ^) >> main.spec

REM 执行打包
echo 🚀 开始打包...
pyinstaller main.spec

REM 检查打包结果
if exist "dist\WebDataAutomation.exe" (
    echo ✅ 打包成功！
    echo 📁 可执行文件位置: dist\WebDataAutomation.exe
    echo.
    echo 📋 使用说明:
    echo 1. 将 dist\WebDataAutomation.exe 复制到目标机器
    echo 2. 在同目录创建 config 文件夹
    echo 3. 复制配置文件到 config 文件夹
    echo 4. 运行 WebDataAutomation.exe
) else (
    echo ❌ 打包失败！
    echo 请检查错误信息
)

echo.
pause
deactivate
