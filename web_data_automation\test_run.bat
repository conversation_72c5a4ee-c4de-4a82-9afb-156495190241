@echo off
echo ========================================
echo 诊断脚本 - 检查运行环境
echo ========================================
echo.

echo 当前目录: %CD%
echo 脚本位置: %~dp0
echo.

echo 🔍 检查Python...
python --version
if %errorlevel% neq 0 (
    echo ❌ Python未找到
    echo.
    echo 请检查:
    echo 1. Python是否已安装
    echo 2. Python是否添加到PATH环境变量
    echo.
    echo 手动测试: 打开命令提示符，输入 python --version
    goto :end
) else (
    echo ✅ Python已安装
)

echo.
echo 🔍 检查pip...
pip --version
if %errorlevel% neq 0 (
    echo ❌ pip未找到
    goto :end
) else (
    echo ✅ pip已安装
)

echo.
echo 🔍 检查文件结构...
if exist "main.py" (
    echo ✅ main.py 存在
) else (
    echo ❌ main.py 不存在
)

if exist "requirements.txt" (
    echo ✅ requirements.txt 存在
) else (
    echo ❌ requirements.txt 不存在
)

if exist "config" (
    echo ✅ config 目录存在
) else (
    echo ❌ config 目录不存在
)

if exist "config\config.yaml" (
    echo ✅ config.yaml 存在
) else (
    echo ❌ config.yaml 不存在
)

if exist "config\.env.example" (
    echo ✅ .env.example 存在
) else (
    echo ❌ .env.example 不存在
)

echo.
echo 🔍 检查权限...
echo test > test_write.tmp 2>nul
if exist "test_write.tmp" (
    echo ✅ 目录可写
    del test_write.tmp
) else (
    echo ❌ 目录不可写，请以管理员身份运行
)

echo.
echo 🔍 尝试创建虚拟环境...
if not exist "test_venv" (
    python -m venv test_venv
    if %errorlevel% equ 0 (
        echo ✅ 虚拟环境创建成功
        rmdir /s /q test_venv
    ) else (
        echo ❌ 虚拟环境创建失败
    )
) else (
    echo ⚠️ 测试虚拟环境已存在
)

:end
echo.
echo ========================================
echo 诊断完成
echo ========================================
echo.
echo 如果所有检查都通过，请尝试:
echo 1. 右键点击 run.bat，选择"以管理员身份运行"
echo 2. 或者打开命令提示符，cd到项目目录，运行 run.bat
echo.
pause
