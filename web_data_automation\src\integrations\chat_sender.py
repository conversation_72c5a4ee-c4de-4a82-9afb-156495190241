"""
聊天软件集成模块
支持企业微信、钉钉、飞书等聊天软件的文件发送
"""

import os
import json
import requests
import hashlib
import hmac
import base64
import time
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)


class ChatSender:
    """聊天软件发送器基类"""
    
    def __init__(self, config):
        """
        初始化聊天发送器
        
        Args:
            config: 配置对象
        """
        self.config = config
        self.chat_config = config.get('chat', {})
    
    def send_file(self, file_path: str, message: str = "") -> bool:
        """
        发送文件到聊天软件
        
        Args:
            file_path: 文件路径
            message: 附加消息
            
        Returns:
            bool: 发送是否成功
        """
        raise NotImplementedError("Subclasses must implement send_file method")
    
    def send_message(self, message: str) -> bool:
        """
        发送文本消息
        
        Args:
            message: 消息内容
            
        Returns:
            bool: 发送是否成功
        """
        raise NotImplementedError("Subclasses must implement send_message method")


class WeChatSender(ChatSender):
    """企业微信发送器"""
    
    def __init__(self, config):
        super().__init__(config)
        self.webhook_url = self.chat_config.get('wechat.webhook_url')
        self.group_id = self.chat_config.get('wechat.group_id')
    
    def send_message(self, message: str) -> bool:
        """发送文本消息到企业微信"""
        try:
            if not self.webhook_url:
                logger.error("WeChat webhook URL not configured")
                return False
            
            payload = {
                "msgtype": "text",
                "text": {
                    "content": message
                }
            }
            
            response = requests.post(self.webhook_url, json=payload)
            response.raise_for_status()
            
            result = response.json()
            if result.get('errcode') == 0:
                logger.info("WeChat message sent successfully")
                return True
            else:
                logger.error(f"WeChat message failed: {result}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to send WeChat message: {str(e)}")
            return False
    
    def send_file(self, file_path: str, message: str = "") -> bool:
        """发送文件到企业微信"""
        try:
            # 企业微信需要先上传文件获取media_id，然后发送
            # 这里简化为发送文件信息
            file_name = os.path.basename(file_path)
            file_size = os.path.getsize(file_path)
            
            content = f"📊 数据文件已生成\n"
            content += f"文件名: {file_name}\n"
            content += f"文件大小: {file_size / 1024:.2f} KB\n"
            if message:
                content += f"说明: {message}"
            
            return self.send_message(content)
            
        except Exception as e:
            logger.error(f"Failed to send WeChat file: {str(e)}")
            return False


class DingTalkSender(ChatSender):
    """钉钉发送器"""
    
    def __init__(self, config):
        super().__init__(config)
        self.webhook_url = self.chat_config.get('dingtalk.webhook_url')
        self.secret = self.chat_config.get('dingtalk.secret')
    
    def _generate_sign(self, timestamp: str) -> str:
        """生成钉钉签名"""
        if not self.secret:
            return ""
        
        string_to_sign = f"{timestamp}\n{self.secret}"
        hmac_code = hmac.new(
            self.secret.encode('utf-8'),
            string_to_sign.encode('utf-8'),
            digestmod=hashlib.sha256
        ).digest()
        sign = base64.b64encode(hmac_code).decode('utf-8')
        return sign
    
    def send_message(self, message: str) -> bool:
        """发送文本消息到钉钉"""
        try:
            if not self.webhook_url:
                logger.error("DingTalk webhook URL not configured")
                return False
            
            timestamp = str(round(time.time() * 1000))
            sign = self._generate_sign(timestamp)
            
            url = self.webhook_url
            if sign:
                url += f"&timestamp={timestamp}&sign={sign}"
            
            payload = {
                "msgtype": "text",
                "text": {
                    "content": message
                }
            }
            
            response = requests.post(url, json=payload)
            response.raise_for_status()
            
            result = response.json()
            if result.get('errcode') == 0:
                logger.info("DingTalk message sent successfully")
                return True
            else:
                logger.error(f"DingTalk message failed: {result}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to send DingTalk message: {str(e)}")
            return False
    
    def send_file(self, file_path: str, message: str = "") -> bool:
        """发送文件信息到钉钉"""
        try:
            file_name = os.path.basename(file_path)
            file_size = os.path.getsize(file_path)
            
            content = f"📊 **数据文件已生成**\n\n"
            content += f"**文件名:** {file_name}\n"
            content += f"**文件大小:** {file_size / 1024:.2f} KB\n"
            content += f"**生成时间:** {time.strftime('%Y-%m-%d %H:%M:%S')}\n"
            if message:
                content += f"**说明:** {message}"
            
            return self.send_message(content)
            
        except Exception as e:
            logger.error(f"Failed to send DingTalk file: {str(e)}")
            return False


class FeishuSender(ChatSender):
    """飞书发送器"""
    
    def __init__(self, config):
        super().__init__(config)
        self.webhook_url = self.chat_config.get('feishu.webhook_url')
    
    def send_message(self, message: str) -> bool:
        """发送文本消息到飞书"""
        try:
            if not self.webhook_url:
                logger.error("Feishu webhook URL not configured")
                return False
            
            payload = {
                "msg_type": "text",
                "content": {
                    "text": message
                }
            }
            
            response = requests.post(self.webhook_url, json=payload)
            response.raise_for_status()
            
            result = response.json()
            if result.get('code') == 0:
                logger.info("Feishu message sent successfully")
                return True
            else:
                logger.error(f"Feishu message failed: {result}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to send Feishu message: {str(e)}")
            return False
    
    def send_file(self, file_path: str, message: str = "") -> bool:
        """发送文件信息到飞书"""
        try:
            file_name = os.path.basename(file_path)
            file_size = os.path.getsize(file_path)
            
            content = f"📊 数据文件已生成\n"
            content += f"文件名: {file_name}\n"
            content += f"文件大小: {file_size / 1024:.2f} KB\n"
            content += f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n"
            if message:
                content += f"说明: {message}"
            
            return self.send_message(content)
            
        except Exception as e:
            logger.error(f"Failed to send Feishu file: {str(e)}")
            return False


class CustomWebhookSender(ChatSender):
    """自定义Webhook发送器"""
    
    def __init__(self, config):
        super().__init__(config)
        self.webhook_url = self.chat_config.get('custom.webhook_url')
        self.method = self.chat_config.get('custom.method', 'POST')
        self.headers = self.chat_config.get('custom.headers', {})
    
    def send_message(self, message: str) -> bool:
        """发送消息到自定义Webhook"""
        try:
            if not self.webhook_url:
                logger.error("Custom webhook URL not configured")
                return False
            
            payload = {
                "message": message,
                "timestamp": time.time(),
                "type": "text"
            }
            
            response = requests.request(
                method=self.method,
                url=self.webhook_url,
                json=payload,
                headers=self.headers
            )
            response.raise_for_status()
            
            logger.info("Custom webhook message sent successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send custom webhook message: {str(e)}")
            return False
    
    def send_file(self, file_path: str, message: str = "") -> bool:
        """发送文件到自定义Webhook"""
        try:
            if not self.webhook_url:
                logger.error("Custom webhook URL not configured")
                return False
            
            # 发送文件信息
            file_info = {
                "file_name": os.path.basename(file_path),
                "file_size": os.path.getsize(file_path),
                "file_path": file_path,
                "message": message,
                "timestamp": time.time(),
                "type": "file"
            }
            
            response = requests.request(
                method=self.method,
                url=self.webhook_url,
                json=file_info,
                headers=self.headers
            )
            response.raise_for_status()
            
            logger.info("Custom webhook file info sent successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send custom webhook file: {str(e)}")
            return False


class ChatSenderFactory:
    """聊天发送器工厂类"""
    
    @staticmethod
    def create_sender(config) -> Optional[ChatSender]:
        """
        根据配置创建相应的聊天发送器
        
        Args:
            config: 配置对象
            
        Returns:
            ChatSender: 聊天发送器实例
        """
        chat_config = config.get('chat', {})
        
        # 检查哪个聊天软件被启用
        if chat_config.get('wechat.enabled', False):
            return WeChatSender(config)
        elif chat_config.get('dingtalk.enabled', False):
            return DingTalkSender(config)
        elif chat_config.get('feishu.enabled', False):
            return FeishuSender(config)
        elif chat_config.get('custom.enabled', False):
            return CustomWebhookSender(config)
        else:
            logger.warning("No chat sender enabled in configuration")
            return None
