# 故障排除指南

## 🚨 双击 run.bat 没反应的解决方案

### 方法1: 使用诊断脚本
1. 双击运行 `test_run.bat`
2. 查看诊断结果，根据提示解决问题

### 方法2: 手动运行
1. 按 `Win + R` 打开运行对话框
2. 输入 `cmd` 并按回车
3. 在命令提示符中输入以下命令：
```cmd
cd /d "C:\path\to\web_data_automation"
run.bat
```

### 方法3: 以管理员身份运行
1. 右键点击 `run.bat`
2. 选择"以管理员身份运行"

### 方法4: 检查文件关联
1. 右键点击 `run.bat`
2. 选择"打开方式" → "选择其他应用"
3. 选择"命令提示符"或"Windows 命令处理器"

## 🔧 常见问题解决

### 问题1: Python未安装或未添加到PATH
**症状:** 提示"Python未安装或未添加到PATH"

**解决方案:**
1. 下载Python: https://www.python.org/downloads/
2. 安装时**务必勾选** "Add Python to PATH"
3. 重启电脑
4. 验证安装: 打开命令提示符，输入 `python --version`

### 问题2: 权限不足
**症状:** 创建虚拟环境失败或文件无法写入

**解决方案:**
1. 以管理员身份运行脚本
2. 或将项目移动到用户目录下（如桌面）

### 问题3: 网络连接问题
**症状:** 依赖包安装失败

**解决方案:**
```cmd
# 使用国内镜像源
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 问题4: 杀毒软件拦截
**症状:** 脚本运行被阻止

**解决方案:**
1. 将项目文件夹添加到杀毒软件白名单
2. 临时关闭实时保护

### 问题5: 中文路径问题
**症状:** 路径包含中文字符导致错误

**解决方案:**
1. 将项目移动到纯英文路径下
2. 如: `C:\projects\web_data_automation\`

## 🛠️ 手动安装步骤

如果自动脚本完全无法运行，请按以下步骤手动安装：

### 步骤1: 检查Python
```cmd
python --version
pip --version
```

### 步骤2: 创建虚拟环境
```cmd
cd /d "项目目录路径"
python -m venv venv
```

### 步骤3: 激活虚拟环境
```cmd
venv\Scripts\activate
```

### 步骤4: 安装依赖
```cmd
pip install -r requirements.txt
```

### 步骤5: 配置环境变量
```cmd
copy config\.env.example config\.env
notepad config\.env
```

### 步骤6: 运行程序
```cmd
python main.py
```

## 🔍 详细诊断

### 检查Python安装
```cmd
where python
python -c "import sys; print(sys.executable)"
python -c "import sys; print(sys.version)"
```

### 检查pip配置
```cmd
pip config list
pip list
```

### 检查环境变量
```cmd
echo %PATH%
echo %PYTHONPATH%
```

### 检查文件权限
```cmd
icacls "项目目录" /T
```

## 📞 获取帮助

如果以上方法都无法解决问题，请：

1. **收集信息:**
   - 运行 `test_run.bat` 的完整输出
   - Python版本: `python --version`
   - 操作系统版本
   - 错误截图

2. **检查日志:**
   - 查看 `logs` 目录下的日志文件
   - 查看Windows事件查看器

3. **替代方案:**
   - 使用Python IDE（如PyCharm、VSCode）直接运行
   - 使用Anaconda环境
   - 使用Docker容器

## 🚀 快速启动（无脚本）

如果脚本问题无法解决，可以直接使用以下命令：

```cmd
# 1. 打开命令提示符
# 2. 切换到项目目录
cd /d "C:\path\to\web_data_automation"

# 3. 创建并激活虚拟环境
python -m venv venv
venv\Scripts\activate

# 4. 安装依赖
pip install selenium webdriver-manager requests pandas openpyxl python-dotenv schedule PyYAML

# 5. 配置环境变量
copy config\.env.example config\.env
# 编辑 config\.env 文件

# 6. 运行程序
python main.py
```

## 💡 预防措施

为避免类似问题，建议：

1. **使用英文路径:** 避免中文字符
2. **定期更新:** 保持Python和pip最新版本
3. **权限管理:** 使用管理员权限或用户目录
4. **网络配置:** 配置pip镜像源
5. **环境隔离:** 使用虚拟环境避免冲突
