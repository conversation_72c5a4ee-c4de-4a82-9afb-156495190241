"""
Simple RPA Tool - 最终修复版打包脚本
解决所有已知的兼容性问题
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path


def print_step(step, message):
    """打印步骤信息"""
    print(f"\n{'='*10} 步骤 {step} {'='*10}")
    print(message)
    print('='*30)


def check_environment():
    """检查打包环境"""
    print_step(1, "检查打包环境")
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ Python版本过低，需要3.7或更高版本")
        return False
    print(f"✅ Python版本: {sys.version}")
    
    # 检查PyInstaller
    try:
        import PyInstaller
        print(f"✅ PyInstaller已安装: {PyInstaller.__version__}")
    except ImportError:
        print("⚠️ 正在安装PyInstaller...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✅ PyInstaller安装完成")
        except subprocess.CalledProcessError:
            print("❌ PyInstaller安装失败")
            return False
    
    # 检查必要文件
    required_files = [
        'main_fixed.py',
        'gui/main_window.py',
        'core/task_engine.py',
        'core/action_types.py',
        'config/default_config.json',
        'templates/hello_world.json'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    
    print("✅ 所有必要文件都存在")
    return True


def clean_build_dirs():
    """清理构建目录"""
    print_step(2, "清理旧的构建文件")
    
    dirs_to_clean = ['build', '__pycache__']
    files_to_clean = ['SimpleRPATool_Final.spec']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"✅ 清理目录: {dir_name}")
    
    for file_name in files_to_clean:
        if os.path.exists(file_name):
            os.remove(file_name)
            print(f"✅ 清理文件: {file_name}")
    
    # 清理Python缓存
    for root, dirs, files in os.walk('.'):
        for dir_name in dirs[:]:
            if dir_name == '__pycache__':
                shutil.rmtree(os.path.join(root, dir_name))
                dirs.remove(dir_name)
        for file_name in files:
            if file_name.endswith('.pyc'):
                os.remove(os.path.join(root, file_name))
    
    print("✅ 清理完成")


def create_build_script():
    """创建构建脚本"""
    print_step(3, "准备打包配置")
    
    current_dir = os.path.abspath(os.path.dirname(__file__))
    
    # 构建PyInstaller命令
    cmd = [
        "pyinstaller",
        "--clean",
        "--onefile",
        "--windowed",
        "--name", "SimpleRPATool_Final",
        
        # 添加数据文件
        "--add-data", f"{os.path.join(current_dir, 'templates')};templates",
        "--add-data", f"{os.path.join(current_dir, 'config')};config",
        
        # 只包含必要的tkinter模块
        "--hidden-import", "tkinter",
        "--hidden-import", "tkinter.ttk",
        "--hidden-import", "tkinter.messagebox",
        "--hidden-import", "tkinter.filedialog",
        
        # 排除所有可能导致问题的模块
        "--exclude-module", "pyautogui",
        "--exclude-module", "keyboard",
        "--exclude-module", "mouse",
        "--exclude-module", "pynput",
        "--exclude-module", "PIL",
        "--exclude-module", "Pillow",
        "--exclude-module", "numpy",
        "--exclude-module", "opencv-python",
        "--exclude-module", "cv2",
        "--exclude-module", "selenium",
        "--exclude-module", "requests",
        "--exclude-module", "openpyxl",
        "--exclude-module", "pandas",
        "--exclude-module", "xlsxwriter",
        "--exclude-module", "psutil",
        "--exclude-module", "schedule",
        "--exclude-module", "APScheduler",
        "--exclude-module", "loguru",
        "--exclude-module", "pyyaml",
        "--exclude-module", "cryptography",
        "--exclude-module", "tqdm",
        "--exclude-module", "colorama",
        "--exclude-module", "click",
        "--exclude-module", "httpx",
        "--exclude-module", "websockets",
        
        # 设置路径
        "--paths", current_dir,
        
        # 主程序文件
        os.path.join(current_dir, "main_fixed.py")
    ]
    
    return cmd


def build_executable():
    """构建可执行文件"""
    print_step(4, "开始打包程序")
    
    cmd = create_build_script()
    
    print("执行命令:")
    print(" ".join(cmd))
    print()
    
    try:
        # 执行打包命令
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ 打包成功!")
        else:
            print("❌ 打包失败!")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 打包过程中出现异常: {e}")
        return False
    
    return True


def verify_build():
    """验证构建结果"""
    print_step(5, "验证构建结果")
    
    exe_path = Path("dist/SimpleRPATool_Final.exe")
    
    if not exe_path.exists():
        print("❌ 可执行文件不存在")
        return False
    
    # 检查文件大小
    size = exe_path.stat().st_size
    size_mb = size / (1024 * 1024)
    
    print(f"✅ 可执行文件已生成")
    print(f"📦 文件位置: {exe_path}")
    print(f"📁 文件大小: {size:,} 字节 ({size_mb:.1f} MB)")
    
    # 检查是否过大
    if size_mb > 100:
        print("⚠️ 文件较大，可能包含了不必要的依赖")
    elif size_mb < 5:
        print("⚠️ 文件较小，可能缺少必要的组件")
    else:
        print("✅ 文件大小合理")
    
    return True


def test_executable():
    """测试可执行文件"""
    print_step(6, "测试可执行文件")
    
    exe_path = Path("dist/SimpleRPATool_Final.exe")
    
    if not exe_path.exists():
        print("❌ 可执行文件不存在，无法测试")
        return False
    
    response = input("是否运行测试程序？(y/n): ").lower().strip()
    
    if response == 'y':
        try:
            print("🧪 启动测试程序...")
            subprocess.Popen([str(exe_path)])
            print("✅ 程序已启动，请检查是否正常运行")
            
            input("按回车键继续...")
            return True
            
        except Exception as e:
            print(f"❌ 启动测试失败: {e}")
            return False
    else:
        print("⏭️ 跳过测试")
        return True


def main():
    """主函数"""
    print("Simple RPA Tool - 最终修复版打包工具")
    print("="*50)
    
    # 步骤1: 检查环境
    if not check_environment():
        print("\n❌ 环境检查失败，无法继续打包")
        input("按回车键退出...")
        return
    
    # 步骤2: 清理旧文件
    clean_build_dirs()
    
    # 步骤3-4: 构建可执行文件
    if not build_executable():
        print("\n❌ 打包失败")
        input("按回车键退出...")
        return
    
    # 步骤5: 验证构建结果
    if not verify_build():
        print("\n❌ 构建验证失败")
        input("按回车键退出...")
        return
    
    # 步骤6: 测试可执行文件
    test_executable()
    
    # 完成
    print("\n" + "="*50)
    print("🎉 打包完成!")
    print("="*50)
    print()
    print("📋 使用说明:")
    print("1. 生成的文件: dist/SimpleRPATool_Final.exe")
    print("2. 此版本已修复所有已知的兼容性问题")
    print("3. 可以在没有Python环境的电脑上运行")
    print("4. 基本功能完整，高级功能可能受限")
    print("5. 如遇问题请查看TROUBLESHOOTING.md")
    print()
    print("感谢使用 Simple RPA Tool!")
    
    input("按回车键退出...")


if __name__ == "__main__":
    main()
