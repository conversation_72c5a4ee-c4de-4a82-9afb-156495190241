@echo off
setlocal enabledelayedexpansion
cd /d "%~dp0"

REM 设置控制台编码为UTF-8
chcp 65001 >nul 2>&1

echo ========================================
echo Web Data Automation 启动脚本
echo ========================================
echo 当前目录: %CD%
echo 脚本位置: %~dp0
echo.

REM 检查Python是否安装
echo 🔍 检查Python安装...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python未安装或未添加到PATH
    echo.
    echo 解决方案:
    echo 1. 从 https://www.python.org/downloads/ 下载Python 3.8+
    echo 2. 安装时勾选 "Add Python to PATH"
    echo 3. 重启命令提示符后重试
    echo.
    pause
    exit /b 1
) else (
    python --version
    echo ✅ Python已安装
)

REM 检查虚拟环境
echo.
echo 🔍 检查虚拟环境...
if not exist "venv" (
    echo 🔧 创建虚拟环境...
    python -m venv venv
    if %errorlevel% neq 0 (
        echo ❌ 创建虚拟环境失败
        echo 可能原因: 权限不足或磁盘空间不够
        pause
        exit /b 1
    ) else (
        echo ✅ 虚拟环境创建成功
    )
) else (
    echo ✅ 虚拟环境已存在
)

REM 激活虚拟环境
echo 🔧 激活虚拟环境...
if exist "venv\Scripts\activate.bat" (
    call venv\Scripts\activate.bat
    echo ✅ 虚拟环境激活成功
) else (
    echo ❌ 虚拟环境激活脚本不存在
    pause
    exit /b 1
)

REM 安装依赖
echo.
echo 🔍 检查依赖包...
if not exist "venv\installed.flag" (
    echo 📦 安装依赖包...
    echo 这可能需要几分钟时间，请耐心等待...
    pip install -r requirements.txt
    if %errorlevel% equ 0 (
        echo. > venv\installed.flag
        echo ✅ 依赖安装完成
    ) else (
        echo ❌ 依赖安装失败
        echo 请检查网络连接或尝试使用国内镜像源
        pause
        exit /b 1
    )
) else (
    echo ✅ 依赖包已安装
)

REM 检查配置文件
echo.
echo 🔍 检查配置文件...
if not exist "config\.env" (
    echo ⚠️  配置文件不存在
    echo.
    echo 请按以下步骤配置:
    echo 1. 复制 config\.env.example 为 config\.env
    echo 2. 编辑 config\.env 填入实际配置
    echo 3. 重新运行此脚本
    echo.
    if exist "config\.env.example" (
        echo 正在自动复制配置模板...
        copy "config\.env.example" "config\.env" >nul
        echo ✅ 配置模板已复制，请编辑 config\.env 文件
    )
    pause
    exit /b 1
) else (
    echo ✅ 配置文件存在
)

REM 运行程序
echo.
echo 🚀 启动程序...
python main.py %*

REM 保持窗口打开
echo.
if %errorlevel% neq 0 (
    echo ❌ 程序执行出错，错误代码: %errorlevel%
) else (
    echo ✅ 程序执行完成
)
echo.
echo 按任意键退出...
pause >nul

REM 停用虚拟环境
if defined VIRTUAL_ENV (
    deactivate
)
