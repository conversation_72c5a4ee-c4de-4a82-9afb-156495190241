"""
文件管理模块
负责数据文件的保存、管理和归档
"""

import os
import shutil
import zipfile
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Optional
import pandas as pd
import logging

logger = logging.getLogger(__name__)


class FileManager:
    """文件管理器"""
    
    def __init__(self, config):
        """
        初始化文件管理器
        
        Args:
            config: 配置对象
        """
        self.config = config
        self.output_config = config.get('output', {})
        self.base_path = self.output_config.get('base_path', 'output')
        self.filename_template = self.output_config.get('filename_template', 'data_{timestamp}')
        self.formats = self.output_config.get('formats', ['xlsx'])
        
        # 确保输出目录存在
        self._ensure_directories()
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        try:
            # 创建基础输出目录
            os.makedirs(self.base_path, exist_ok=True)
            
            # 创建归档目录
            archive_config = self.output_config.get('archive', {})
            if archive_config.get('enabled', True):
                archive_path = archive_config.get('archive_path', 'output/archive')
                os.makedirs(archive_path, exist_ok=True)
            
            logger.info(f"Output directories ensured: {self.base_path}")
            
        except Exception as e:
            logger.error(f"Failed to create directories: {str(e)}")
            raise
    
    def _generate_filename(self, format_ext: str) -> str:
        """
        生成文件名
        
        Args:
            format_ext: 文件扩展名
            
        Returns:
            str: 完整文件名
        """
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = self.filename_template.format(timestamp=timestamp)
        return f"{filename}.{format_ext}"
    
    def save_data(self, df: pd.DataFrame, custom_filename: str = None) -> List[str]:
        """
        保存数据到文件
        
        Args:
            df: 要保存的DataFrame
            custom_filename: 自定义文件名（不含扩展名）
            
        Returns:
            List[str]: 保存的文件路径列表
        """
        saved_files = []
        
        try:
            if df.empty:
                logger.warning("DataFrame is empty, no files will be saved")
                return saved_files
            
            for format_ext in self.formats:
                try:
                    # 生成文件名
                    if custom_filename:
                        filename = f"{custom_filename}.{format_ext}"
                    else:
                        filename = self._generate_filename(format_ext)
                    
                    filepath = os.path.join(self.base_path, filename)
                    
                    # 根据格式保存文件
                    if format_ext.lower() == 'xlsx':
                        self._save_excel(df, filepath)
                    elif format_ext.lower() == 'csv':
                        self._save_csv(df, filepath)
                    elif format_ext.lower() == 'json':
                        self._save_json(df, filepath)
                    else:
                        logger.warning(f"Unsupported format: {format_ext}")
                        continue
                    
                    saved_files.append(filepath)
                    logger.info(f"Data saved to {filepath}")
                    
                except Exception as e:
                    logger.error(f"Failed to save {format_ext} file: {str(e)}")
            
            # 执行归档操作
            self._archive_old_files()
            
            return saved_files
            
        except Exception as e:
            logger.error(f"Failed to save data: {str(e)}")
            return []
    
    def _save_excel(self, df: pd.DataFrame, filepath: str):
        """保存为Excel文件"""
        excel_config = self.output_config.get('excel', {})
        sheet_name = excel_config.get('sheet_name', '数据')
        include_index = excel_config.get('include_index', False)
        
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name=sheet_name, index=include_index)
            
            # 设置列宽自适应
            worksheet = writer.sheets[sheet_name]
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width
    
    def _save_csv(self, df: pd.DataFrame, filepath: str):
        """保存为CSV文件"""
        df.to_csv(filepath, index=False, encoding='utf-8-sig')
    
    def _save_json(self, df: pd.DataFrame, filepath: str):
        """保存为JSON文件"""
        df.to_json(filepath, orient='records', force_ascii=False, indent=2)
    
    def _archive_old_files(self):
        """归档旧文件"""
        try:
            archive_config = self.output_config.get('archive', {})
            if not archive_config.get('enabled', True):
                return
            
            keep_days = archive_config.get('keep_days', 30)
            archive_path = archive_config.get('archive_path', 'output/archive')
            
            # 确保归档目录存在
            os.makedirs(archive_path, exist_ok=True)
            
            cutoff_date = datetime.now() - timedelta(days=keep_days)
            archived_count = 0
            
            # 遍历输出目录中的文件
            for filename in os.listdir(self.base_path):
                filepath = os.path.join(self.base_path, filename)
                
                # 跳过目录
                if os.path.isdir(filepath):
                    continue
                
                # 检查文件修改时间
                file_mtime = datetime.fromtimestamp(os.path.getmtime(filepath))
                
                if file_mtime < cutoff_date:
                    # 移动到归档目录
                    archive_filepath = os.path.join(archive_path, filename)
                    shutil.move(filepath, archive_filepath)
                    archived_count += 1
            
            if archived_count > 0:
                logger.info(f"Archived {archived_count} old files to {archive_path}")
            
        except Exception as e:
            logger.error(f"Failed to archive old files: {str(e)}")
    
    def compress_files(self, file_paths: List[str], archive_name: str = None) -> Optional[str]:
        """
        压缩文件
        
        Args:
            file_paths: 要压缩的文件路径列表
            archive_name: 压缩包名称
            
        Returns:
            str: 压缩包路径
        """
        try:
            if not file_paths:
                return None
            
            if not archive_name:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                archive_name = f"data_archive_{timestamp}.zip"
            
            archive_path = os.path.join(self.base_path, archive_name)
            
            with zipfile.ZipFile(archive_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for file_path in file_paths:
                    if os.path.exists(file_path):
                        # 使用文件名作为压缩包内的路径
                        arcname = os.path.basename(file_path)
                        zipf.write(file_path, arcname)
            
            logger.info(f"Files compressed to {archive_path}")
            return archive_path
            
        except Exception as e:
            logger.error(f"Failed to compress files: {str(e)}")
            return None
    
    def cleanup_temp_files(self):
        """清理临时文件"""
        try:
            temp_patterns = ['*.tmp', '*.temp', '~$*']
            cleaned_count = 0
            
            for pattern in temp_patterns:
                for filepath in Path(self.base_path).glob(pattern):
                    try:
                        filepath.unlink()
                        cleaned_count += 1
                    except Exception as e:
                        logger.warning(f"Failed to delete temp file {filepath}: {str(e)}")
            
            if cleaned_count > 0:
                logger.info(f"Cleaned up {cleaned_count} temporary files")
            
        except Exception as e:
            logger.error(f"Failed to cleanup temp files: {str(e)}")
    
    def get_file_info(self, filepath: str) -> dict:
        """
        获取文件信息
        
        Args:
            filepath: 文件路径
            
        Returns:
            dict: 文件信息
        """
        try:
            if not os.path.exists(filepath):
                return {}
            
            stat = os.stat(filepath)
            
            return {
                'filename': os.path.basename(filepath),
                'size': stat.st_size,
                'size_mb': round(stat.st_size / (1024 * 1024), 2),
                'created': datetime.fromtimestamp(stat.st_ctime),
                'modified': datetime.fromtimestamp(stat.st_mtime),
                'extension': os.path.splitext(filepath)[1].lower()
            }
            
        except Exception as e:
            logger.error(f"Failed to get file info for {filepath}: {str(e)}")
            return {}
    
    def list_output_files(self, days: int = 7) -> List[dict]:
        """
        列出输出目录中的文件
        
        Args:
            days: 列出最近几天的文件
            
        Returns:
            List[dict]: 文件信息列表
        """
        try:
            files_info = []
            cutoff_date = datetime.now() - timedelta(days=days)
            
            for filename in os.listdir(self.base_path):
                filepath = os.path.join(self.base_path, filename)
                
                if os.path.isfile(filepath):
                    file_info = self.get_file_info(filepath)
                    
                    if file_info and file_info['modified'] >= cutoff_date:
                        file_info['path'] = filepath
                        files_info.append(file_info)
            
            # 按修改时间排序
            files_info.sort(key=lambda x: x['modified'], reverse=True)
            
            return files_info
            
        except Exception as e:
            logger.error(f"Failed to list output files: {str(e)}")
            return []
    
    def get_storage_usage(self) -> dict:
        """
        获取存储使用情况
        
        Returns:
            dict: 存储使用信息
        """
        try:
            total_size = 0
            file_count = 0
            
            for root, dirs, files in os.walk(self.base_path):
                for file in files:
                    filepath = os.path.join(root, file)
                    try:
                        total_size += os.path.getsize(filepath)
                        file_count += 1
                    except OSError:
                        pass
            
            return {
                'total_size_bytes': total_size,
                'total_size_mb': round(total_size / (1024 * 1024), 2),
                'file_count': file_count,
                'base_path': self.base_path
            }
            
        except Exception as e:
            logger.error(f"Failed to get storage usage: {str(e)}")
            return {}
