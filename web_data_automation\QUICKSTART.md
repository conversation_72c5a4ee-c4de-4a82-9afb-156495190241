# Web Data Automation 快速开始指南

## 🚀 快速开始

### 1. 环境准备

**系统要求:**
- Windows 10/11
- Python 3.8+
- Chrome浏览器

**安装Python:**
1. 访问 [Python官网](https://www.python.org/downloads/)
2. 下载并安装Python 3.8+
3. 安装时勾选"Add Python to PATH"

### 2. 项目设置

**方法一：使用启动脚本（推荐）**
```bash
# 双击运行 run.bat
# 脚本会自动：
# - 创建虚拟环境
# - 安装依赖包
# - 检查配置文件
# - 启动程序
```

**方法二：手动设置**
```bash
# 1. 创建虚拟环境
python -m venv venv

# 2. 激活虚拟环境
venv\Scripts\activate

# 3. 安装依赖
pip install -r requirements.txt

# 4. 运行程序
python main.py
```

### 3. 配置设置

**创建配置文件:**
```bash
# 复制环境变量模板
copy config\.env.example config\.env

# 编辑配置文件
notepad config\.env
```

**必需配置项:**
```env
# 目标网站信息
TARGET_URL=https://your-target-website.com/login
USERNAME=your_username
PASSWORD=your_password

# 聊天软件配置（选择一个）
WECHAT_WEBHOOK_URL=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xxx
DINGTALK_WEBHOOK_URL=https://oapi.dingtalk.com/robot/send?access_token=xxx
FEISHU_WEBHOOK_URL=https://open.feishu.cn/open-apis/bot/v2/hook/xxx
```

### 4. 运行程序

**交互模式:**
```bash
python main.py
```

**命令行模式:**
```bash
# 执行单次任务
python main.py run

# 启动定时任务
python main.py schedule

# 测试配置
python main.py test
```

### 5. 打包为exe

**使用打包脚本:**
```bash
# 双击运行 build.bat
# 生成的exe文件在 dist 目录
```

**手动打包:**
```bash
pip install pyinstaller
pyinstaller --onefile --windowed main.py
```

## 📋 配置说明

### 网页配置
```yaml
web:
  target_url: "https://example.com/login"
  login:
    username_field: "username"
    password_field: "password"
    login_button: "//button[@type='submit']"
```

### 数据处理配置
```yaml
data_processing:
  cleaning:
    remove_empty_rows: true
    remove_empty_columns: true
  transformation:
    date_columns: ["日期"]
    numeric_columns: ["金额", "数量"]
```

### 定时任务配置
```yaml
scheduler:
  jobs:
    - name: "daily_data_fetch"
      schedule: "0 9 * * *"  # 每天上午9点
      enabled: true
```

## 🔧 常见问题

### Q: Chrome浏览器找不到
**A:** 安装Chrome浏览器或配置Chrome路径
```yaml
selenium:
  chrome_path: "C:/Program Files/Google/Chrome/Application/chrome.exe"
```

### Q: 登录失败
**A:** 检查以下配置：
1. 目标URL是否正确
2. 用户名密码是否正确
3. 页面元素定位是否准确

### Q: 数据获取为空
**A:** 可能原因：
1. 页面加载时间不够
2. 筛选条件设置错误
3. 数据表格结构变化

### Q: 聊天软件发送失败
**A:** 检查：
1. Webhook URL是否正确
2. 网络连接是否正常
3. 机器人权限是否足够

## 📝 使用示例

### 基本使用
```python
from src.config.settings import Config
from src.core.web_scraper import WebScraper

# 加载配置
config = Config('config/config.yaml')

# 使用爬虫
with WebScraper(config) as scraper:
    if scraper.login():
        html_content = scraper.fetch_data()
        # 处理数据...
```

### 定时任务
```python
from src.scheduler.task_scheduler import TaskScheduler

scheduler = TaskScheduler(config)
scheduler.register_job(
    name="daily_task",
    func=my_task_function,
    schedule_expr="0 9 * * *"
)
scheduler.start()
```

## 🛠️ 高级功能

### 自定义数据处理
```python
# 在 src/core/data_processor.py 中添加自定义处理逻辑
def _apply_custom_transformations(self, df):
    # 添加自定义列
    df['处理时间'] = datetime.now()
    
    # 数据分类
    df['金额等级'] = pd.cut(df['金额'], 
                          bins=[0, 1000, 5000, float('inf')], 
                          labels=['小额', '中额', '大额'])
    return df
```

### 多网站支持
```yaml
# 配置多个网站
websites:
  site1:
    url: "https://site1.com"
    username: "user1"
    password: "pass1"
  site2:
    url: "https://site2.com"
    username: "user2"
    password: "pass2"
```

### 错误处理和重试
```python
# 配置重试机制
requests:
  max_retries: 3
  retry_delay: 5
  timeout: 30
```

## 📞 技术支持

如果遇到问题，请：
1. 查看日志文件 `logs/automation.log`
2. 检查配置文件是否正确
3. 确认网络连接正常
4. 联系技术支持

## 🔄 更新日志

### v1.0.0
- 初始版本发布
- 支持Selenium + Requests数据获取
- 支持多种聊天软件推送
- 支持定时任务调度
- 支持打包为exe文件
